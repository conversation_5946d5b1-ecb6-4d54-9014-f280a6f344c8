import '@testing-library/jest-dom';

import { User } from '@prisma/client';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useCurrentUser } from '@/contexts/user-context';
import { useApiServices } from '@/hooks/__mocks__/use-api-services';
import { ProfilePicture } from '../profile-picture';

// Mock the useApiServices hook
jest.mock('@/hooks/use-api-services', () => ({
  useApiServices,
}));

// Mock dependencies
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string, params?: Record<string, string>) => {
    if (key === 'fileTooLarge' && params?.size) {
      return `File too large. Maximum size is ${params.size}MB`;
    }
    return key;
  },
}));

jest.mock('@/contexts/user-context', () => ({
  useCurrentUser: jest.fn(),
}));

jest.mock('firebase/storage', () => ({
  ref: jest.fn(),
  uploadBytesResumable: jest.fn(),
}));

jest.mock('@/services/firebase/client-app', () => ({
  storage: {},
}));

const mockUseCurrentUser = useCurrentUser as jest.MockedFunction<typeof useCurrentUser>;

// Mock data
const mockUser: User = {
  id: 'user-1',
  displayName: 'Test User',
  email: '<EMAIL>',
  avatar: null,
  onboarding: false,
  phone: null,
  country: null,
  timezone: 'UTC',
  language: 'en-US',
  theme: 'light',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

// Mock FileReader
const mockFileReader = {
  readAsDataURL: jest.fn(),
  onloadend: jest.fn(),
  result: 'data:image/png;base64,mocked-content',
  onerror: jest.fn(),
};
global.FileReader = jest.fn(() => mockFileReader) as any;

const mockSetValueAction = jest.fn();
const mockOnFileSelectedAction = jest.fn();
const mockSetFormAlertAction = jest.fn(); // Added mock for setFormAlertAction

describe('ProfilePicture', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset and configure FileReader mock for each test
    mockFileReader.readAsDataURL.mockImplementation(function (mocked: any) {
      // Simulate async file reading
      // @ts-ignore
      if (mocked.onloadend) {
        // @ts-ignore
        mocked.onloadend();
      }
    });
    mockFileReader.result = 'data:image/png;base64,mocked-content';
    mockFileReader.onerror.mockReset();

    mockUseCurrentUser.mockReturnValue({
      user: mockUser,
      loading: false,
      error: null,
      fetchUser: jest.fn(),
      updateUser: jest.fn(),
      setLoading: jest.fn(),
    });
  });
  it('should render user avatar', () => {
    render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        setFormAlertAction={mockSetFormAlertAction}
      />
    );

    // Check if UserAvatar is rendered with initials (since no avatar is provided)
    const avatarElement = screen.getByText('TU'); // Test User initials
    expect(avatarElement).toBeInTheDocument();
  });
  it('should show upload overlay on hover', async () => {
    const user = userEvent.setup();
    render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        setFormAlertAction={mockSetFormAlertAction}
      />
    );

    // Find the avatar container by the avatar element
    const avatarElement = screen.getByText('TU');
    const avatarContainer = avatarElement.closest('[data-testid], .MuiBox-root') || avatarElement.parentElement;
    expect(avatarContainer).toBeInTheDocument();

    // Hover over the avatar container
    await user.hover(avatarContainer!);

    // Should show upload text
    expect(screen.getByText('upload')).toBeInTheDocument();
  });
  it('should show remove button when user has avatar', () => {
    const userWithAvatar = { ...mockUser, avatar: 'user-avatars/user-1/profile' };
    mockUseCurrentUser.mockReturnValue({
      user: userWithAvatar,
      loading: false,
      error: null,
      fetchUser: jest.fn(),
      updateUser: jest.fn(),
      setLoading: jest.fn(),
    });

    render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        formAvatarPath={userWithAvatar.avatar}
        setFormAlertAction={mockSetFormAlertAction} // Added prop
      />
    );

    expect(screen.getByText('remove')).toBeInTheDocument();
  });

  it('should not show remove button when user has no avatar', () => {
    render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        setFormAlertAction={mockSetFormAlertAction}
      />
    );

    expect(screen.queryByText('remove')).not.toBeInTheDocument();
  });
  it('should validate file type and call onFileSelectedAction with null', async () => {
    const { container } = render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        setFormAlertAction={mockSetFormAlertAction}
      />
    );

    // Create a mock file with invalid type
    const invalidFile = new File(['test data content'], 'test.gif', { type: 'image/gif' });

    const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;
    expect(fileInput).toBeInTheDocument();

    // Manually set the files property and trigger change event
    Object.defineProperty(fileInput, 'files', {
      value: [invalidFile],
      writable: false, // Important for userEvent.upload to not override if used later
    });

    // Trigger the change event
    fireEvent.change(fileInput);

    // Should show error message
    await waitFor(() => {
      // expect(screen.getByText('invalidFileType')).toBeInTheDocument();
      expect(mockSetFormAlertAction).toHaveBeenCalledWith({ type: 'error', message: 'invalidFileType' });
      expect(mockOnFileSelectedAction).toHaveBeenCalledWith(null);
    });
  });

  it('should validate file size and call onFileSelectedAction with null', async () => {
    const user = userEvent.setup();
    const { container } = render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        setFormAlertAction={mockSetFormAlertAction}
      />
    );

    // Create a mock file that's too large (6MB)
    const largeFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });

    const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;
    expect(fileInput).toBeInTheDocument();

    // Upload large file
    await user.upload(fileInput, largeFile);

    // Should show error message
    await waitFor(() => {
      // expect(screen.getByText('File too large. Maximum size is 5MB')).toBeInTheDocument();
      expect(mockSetFormAlertAction).toHaveBeenCalledWith({
        type: 'error',
        message: 'File too large. Maximum size is 2MB',
      });
      expect(mockOnFileSelectedAction).toHaveBeenCalledWith(null);
    });
  });

  it('should allow removing an existing avatar', async () => {
    const user = userEvent.setup();
    const userWithAvatar = { ...mockUser, avatar: 'user-avatars/user-1/profile' };
    mockUseCurrentUser.mockReturnValue({
      user: userWithAvatar,
      loading: false,
      error: null,
      fetchUser: jest.fn(),
      updateUser: jest.fn(),
      setLoading: jest.fn(),
    });

    render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        formAvatarPath={userWithAvatar.avatar}
        setFormAlertAction={mockSetFormAlertAction} // Added prop
      />
    );

    const removeButton = screen.getByText('remove');
    expect(removeButton).toBeInTheDocument();
    await user.click(removeButton);

    expect(mockSetValueAction).toHaveBeenCalledWith('avatar', '', { shouldDirty: true });
    expect(mockOnFileSelectedAction).toHaveBeenCalledWith(null);
    expect(mockSetFormAlertAction).toHaveBeenCalledWith(null); // Ensure alert is cleared
  });

  it('should allow canceling a new avatar selection', async () => {
    const user = userEvent.setup();
    const { container } = render(
      <ProfilePicture
        setValueAction={mockSetValueAction}
        onFileSelectedAction={mockOnFileSelectedAction}
        setFormAlertAction={mockSetFormAlertAction}
      />
    );

    const fileInput = container.querySelector('input[type="file"]') as HTMLInputElement;
    const validFile = new File(['(⌐□_□)'], 'chucknorris.png', { type: 'image/png' });

    mockFileReader.readAsDataURL.mockImplementation(function (_this: any) {
      // @ts-ignore
      this.result = 'data:image/png;base64,preview-content';
      // @ts-ignore
      if (this.onloadend) {
        // @ts-ignore
        this.onloadend();
      }
    });

    await user.upload(fileInput, validFile);

    await waitFor(() => {
      expect(screen.getByText('cancel')).toBeInTheDocument();
      expect(mockOnFileSelectedAction).toHaveBeenCalledWith(validFile);
      expect(mockSetValueAction).toHaveBeenCalledWith('avatar', 'data:image/png;base64,preview-content', {
        shouldDirty: true,
      });
      expect(mockSetFormAlertAction).toHaveBeenCalledWith(null); // Ensure alert is cleared on successful upload
    });

    mockOnFileSelectedAction.mockClear();

    const cancelButton = screen.getByText('cancel');
    await user.click(cancelButton);

    expect(mockOnFileSelectedAction).toHaveBeenCalledWith(null);
    expect(mockSetFormAlertAction).toHaveBeenCalledWith(null); // Ensure alert is cleared on cancel
    await waitFor(() => {
      expect(screen.queryByText('cancel')).not.toBeInTheDocument();
    });
    expect(screen.queryByText('remove')).not.toBeInTheDocument();
  });

  // Commenting out original tests that were based on non-existent dialog functionality
  // it('should open remove confirmation dialog', async () => { ... });
  // it('should cancel remove operation', async () => { ... });
});
