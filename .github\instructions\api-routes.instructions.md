---
applyTo: '**/route.ts'
---

### API Routes

API must connect to the database to fetch data. We currently use Prisma as the ORM.
The prisma can be accessed through the `db` variable from `import { db } from '@/services/db';`
Prisma schema models are located in the #file:../../prisma/models folder a and sub-folders.

### Authentication

Currently we use firebase as authentication for the API. Here's an example on how to validate user authentication:

```typescript
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/// ... rest of the code

// Get the authenticated user from Firebase
const authResult = await getAuthenticatedAppForUser();

// If no user is authenticated, return 401
if (!authResult?.currentUser) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}

// Now you can access the authenticated user uid using currentUser.uid
const currentUser = authResult.currentUser;

/// ... rest of the code
```

### Swagger

All API routes must be documented using `jsdoc` `@swagger` directly in the jsdoc of the method and you must always keep jsdoc up to date.
Always use $ref to reference prisma types (eg. `$ref: '#/components/schemas/<TypeName>'`)

Example:

```typescript
/**
 * @swagger
 * /api/hello/{name}:
 *   get:
 *     summary: Returns a greeting message
 *     parameters:
 *       - name: name
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the person to greet
 *     responses:
 *       200:
 *         description: A greeting message
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ name: string }> }) {
  const { name } = await params;

  return NextResponse.json({ message: `Hello, ${name}!` });
}
```

> ALWAYS KEEP THE SWAGGER DOCUMENTATION UP TO DATE
> ALWAYS HAVE A SWAGGER JSDOC FOR EACH API ENDPOINT
> DO NOT GROUP DIFFERENT METHODS IN THE SAME JSDOC

> Next.js App Router expects the `params` parameter to be a Promise in newer versions.
