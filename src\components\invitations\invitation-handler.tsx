'use client';

import { useRouter } from '@/i18n/navigation';
import {
  Alert,
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Stack,
  Typography,
} from '@mui/material';
import { BuildingsIcon, CheckIcon, EnvelopeIcon, XIcon } from '@phosphor-icons/react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { useApiServices } from '@/hooks/use-api-services';
import { logger } from '@/lib/logger/default-logger';
import { getTranslatedRole } from '@/lib/models/role';
import { paths } from '@/paths';
import { WorkspaceInvite } from '@prisma/client';

interface InvitationHandlerProps {
  token: string;
}

export function InvitationHandler({ token }: InvitationHandlerProps) {
  const roleTranslations = useTranslations('settings.team');
  const t = useTranslations('workspace.invitations');

  const router = useRouter();
  const { invitesApiService } = useApiServices();

  const { user, loading: authLoading } = useAuth();

  const [invitation, setInvitation] = useState<
    | (WorkspaceInvite & {
        workspace: {
          id: string;
          name: string;
        };
        role: {
          id: string;
          name: string;
        };
        invitedByUser: {
          id: string;
          email: string;
          displayName: string;
        };
      })
    | undefined
  >(undefined);

  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [declining, setDeclining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [actionType, setActionType] = useState<'accept' | 'decline' | null>(null);

  // Fetch invitation details
  useEffect(() => {
    const fetchInvitation = async () => {
      try {
        const response = await invitesApiService.getInvite(token);

        if (response) {
          setInvitation(response);
        } else {
          setError(t('errors.notFound'));
        }
      } catch (err) {
        logger.error('Error fetching invitation:', err);
        setError(t('errors.notFound'));
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [token, invitesApiService, t]);

  const handleAcceptInvitation = async () => {
    if (!user) {
      // Redirect to sign in with return URL
      const returnUrl = `/invite/${token}`;
      window.location.href = `${paths.auth.signIn}?returnTo=${encodeURIComponent(returnUrl)}`;
      return;
    }

    setAccepting(true);
    setError(null);

    try {
      await invitesApiService.acceptInviteByToken(token);
      setSuccess(true);
      setActionType('accept');
      // Don't redirect automatically - let user see success message
    } catch (err: any) {
      logger.error('Error accepting invitation:', err);
      if (err.status === 403) {
        setError(t('errors.emailMismatch'));
      } else if (err.status === 400) {
        setError(err.message || t('errors.acceptFailed'));
      } else {
        setError(t('errors.acceptFailed'));
      }
    } finally {
      setAccepting(false);
    }
  };

  const handleDeclineInvitation = async () => {
    if (!user) {
      // Redirect to sign in with return URL
      const returnUrl = `/invite/${token}`;
      window.location.href = `${paths.auth.signIn}?returnTo=${encodeURIComponent(returnUrl)}`;
      return;
    }

    setDeclining(true);
    setError(null);

    try {
      await invitesApiService.declineInviteByToken(token);
      setSuccess(true);
      setActionType('decline');
      // Don't redirect automatically - let user see success message
    } catch (err: any) {
      logger.error('Error declining invitation:', err);
      if (err.status === 403) {
        setError(t('errors.emailMismatch'));
      } else if (err.status === 400) {
        setError(err.message || t('errors.declineFailed'));
      } else {
        setError(t('errors.declineFailed'));
      }
    } finally {
      setDeclining(false);
    }
  };

  const handleSignIn = () => {
    const returnUrl = `/invite/${token}`;
    window.location.href = `${paths.auth.signIn}?returnTo=${encodeURIComponent(returnUrl)}`;
  };

  const handleSignUp = () => {
    const returnUrl = `/invite/${token}`;
    window.location.href = `${paths.auth.signUp}?returnTo=${encodeURIComponent(returnUrl)}`;
  };

  if (authLoading || loading) {
    return (
      <Container maxWidth='sm' sx={{ py: 8 }}>
        <Box display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth='sm' sx={{ py: 8 }}>
        <Card>
          <CardContent>
            <Stack spacing={3} alignItems='center'>
              <XIcon size={48} color='error' />
              <Typography variant='h5' textAlign='center'>
                {t('invitation.error')}
              </Typography>
              <Alert severity='error'>{error}</Alert>
              <Stack direction='row' spacing={2}>
                <Button variant='outlined' onClick={() => router.push(paths.root)}>
                  {t('actions.goHome')}
                </Button>
                <Button variant='contained' onClick={() => router.push(paths.workspaceSelection)}>
                  {t('actions.goToWorkspaceSelection')}
                </Button>
              </Stack>
            </Stack>
          </CardContent>
        </Card>
      </Container>
    );
  }

  if (success) {
    return (
      <Container maxWidth='sm' sx={{ py: 8 }}>
        <Card>
          <CardContent>
            <Stack spacing={3} alignItems='center'>
              {actionType === 'accept' ? <CheckIcon size={48} color='success' /> : <XIcon size={48} color='warning' />}
              <Typography variant='h5' textAlign='center'>
                {actionType === 'accept' ? t('invitation.success') : t('invitation.declined')}
              </Typography>
              <Alert severity={actionType === 'accept' ? 'success' : 'info'}>
                {actionType === 'accept'
                  ? t('messages.acceptedSuccess', { workspace: invitation?.workspace.name || '' })
                  : t('messages.declinedSuccess', { workspace: invitation?.workspace.name || '' })}
              </Alert>
              <Stack direction='row' spacing={2}>
                <Button variant='outlined' onClick={() => router.push(paths.root)}>
                  {t('actions.goHome')}
                </Button>
                <Button variant='contained' onClick={() => router.push(paths.workspaceSelection)}>
                  {t('actions.goToWorkspaceSelection')}
                </Button>
              </Stack>
            </Stack>
          </CardContent>
        </Card>
      </Container>
    );
  }

  if (!invitation) {
    return null;
  }

  return (
    <Container maxWidth='sm' sx={{ py: 8 }}>
      <Card>
        <CardContent>
          <Stack spacing={4}>
            {/* Header */}
            <Stack spacing={2} alignItems='center'>
              <EnvelopeIcon size={48} />
              <Typography variant='h4' textAlign='center'>
                {t('invitation.title')}
              </Typography>
              <Typography variant='body1' color='text.secondary' textAlign='center'>
                {t('messages.invitedTo', { workspace: invitation.workspace.name })}
              </Typography>
            </Stack>

            {/* Invitation Details - Only show if user is logged in */}
            {user && (
              <Stack spacing={3}>
                <Box>
                  <Typography variant='h6' gutterBottom>
                    <BuildingsIcon size={20} style={{ verticalAlign: 'middle', marginRight: 8 }} />
                    {invitation.workspace.name}
                  </Typography>
                </Box>

                <Stack direction='row' spacing={2} alignItems='center'>
                  <Typography variant='body2' color='text.secondary'>
                    {t('labels.role')}:
                  </Typography>
                  <Chip
                    label={getTranslatedRole(invitation.role, roleTranslations)}
                    color='primary'
                    variant='outlined'
                    size='small'
                  />
                </Stack>

                <Stack direction='row' spacing={2} alignItems='center'>
                  <Typography variant='body2' color='text.secondary'>
                    {t('labels.invitedBy')}:
                  </Typography>
                  <Stack direction='row' spacing={1} alignItems='center'>
                    <Avatar sx={{ width: 24, height: 24 }}>
                      {invitation.invitedByUser.displayName?.[0] || invitation.invitedByUser.email[0]}
                    </Avatar>
                    <Typography variant='body2'>
                      {invitation.invitedByUser.displayName || invitation.invitedByUser.email}
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>
            )}

            {/* Actions */}
            {user ? (
              <Stack spacing={2}>
                {user.email === invitation.email ? (
                  <Stack direction='row' spacing={2}>
                    <Button
                      variant='contained'
                      size='large'
                      onClick={handleAcceptInvitation}
                      disabled={accepting || declining}
                      startIcon={accepting ? <CircularProgress size={20} /> : <CheckIcon />}
                      fullWidth
                    >
                      {accepting ? t('actions.accepting') : t('actions.acceptInvitation')}
                    </Button>
                    <Button
                      variant='outlined'
                      color='error'
                      size='large'
                      onClick={handleDeclineInvitation}
                      disabled={accepting || declining}
                      startIcon={declining ? <CircularProgress size={20} /> : <XIcon />}
                      fullWidth
                    >
                      {declining ? t('declining') : t('decline')}
                    </Button>
                  </Stack>
                ) : (
                  <Alert severity='warning'>
                    {t('errors.wrongAccount', {
                      currentEmail: user.email || '',
                      invitedEmail: invitation.email || '',
                    })}
                  </Alert>
                )}
              </Stack>
            ) : (
              <Stack spacing={2}>
                <Alert severity='info'>{t('messages.signInRequired')}</Alert>
                <Stack direction='row' spacing={2}>
                  <Button variant='contained' onClick={handleSignIn} fullWidth>
                    {t('actions.signIn')}
                  </Button>
                  <Button variant='outlined' onClick={handleSignUp} fullWidth>
                    {t('actions.signUp')}
                  </Button>
                </Stack>
              </Stack>
            )}
          </Stack>
        </CardContent>
      </Card>
    </Container>
  );
}
