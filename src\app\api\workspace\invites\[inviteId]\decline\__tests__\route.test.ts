/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/auth/authentication', () => ({
  isUserAuthenticated: jest.fn(),
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

// Import mocked modules
import { isUserAuthenticated } from '@/lib/auth/authentication';
import { db } from '@/services/db';

import { PATCH } from '../route';

const mockIsUserAuthenticated = isUserAuthenticated as jest.MockedFunction<typeof isUserAuthenticated>;
const mockDb = db as jest.Mocked<typeof db>;

// Mock data
const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'inviter-1',
  userId: 'user-1',
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
};

const mockDeclinedInvitation = {
  ...mockInvitation,
  status: 'REJECTED',
};

describe('/api/workspace/invites/[inviteId]/decline', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('PATCH', () => {
    it('should decline invitation successfully', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceInvite.update as jest.Mock).mockResolvedValue(mockDeclinedInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation declined successfully');
      expect(mockDb.workspaceInvite.update).toHaveBeenCalledWith({
        where: { id: 'invite-1' },
        data: {
          status: 'REJECTED',
          userId: 'user-1',
        },
      });
    });

    it('should return 401 when user is not authenticated', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: false, uid: '', email: '' });

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 400 when invitation is not found', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invitation not found');
    });

    it('should return 400 when invitation is not pending', async () => {
      const acceptedInvitation = { ...mockInvitation, status: 'ACCEPTED' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(acceptedInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invitation has already been processed');
    });

    it('should return 403 when user cannot decline invitation (different email and userId)', async () => {
      const differentUserInvitation = { ...mockInvitation, email: '<EMAIL>', userId: 'different-user' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(differentUserInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });

    it('should decline invitation when user email matches invitation email', async () => {
      const emailMatchInvitation = { ...mockInvitation, userId: null, email: '<EMAIL>' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(emailMatchInvitation);
      (mockDb.workspaceInvite.update as jest.Mock).mockResolvedValue(mockDeclinedInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation declined successfully');
    });

    it('should decline invitation when user uid matches invitation userId', async () => {
      const userIdMatchInvitation = { ...mockInvitation, userId: 'user-1', email: '<EMAIL>' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(userIdMatchInvitation);
      (mockDb.workspaceInvite.update as jest.Mock).mockResolvedValue(mockDeclinedInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation declined successfully');
    });

    it('should handle database errors', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceInvite.update as jest.Mock).mockRejectedValue(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/decline');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to decline invitation');
    });
  });
});
