/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { hashToken } from '@/lib/utils/token';
import { db } from '@/services/db';
import { GET } from '../route';

// Mock the database
jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findFirst: jest.fn(),
    },
  },
}));

// Mock the authentication
jest.mock('@/lib/auth/authentication', () => ({
  isUserAuthenticated: jest.fn(),
}));

const mockDb = {
  workspaceInvite: {
    findFirst: jest.fn(),
  },
} as any;

const mockIsUserAuthenticated = isUserAuthenticated as jest.MockedFunction<typeof isUserAuthenticated>;

// Override the actual db with our mock
(db as any).workspaceInvite = mockDb.workspaceInvite;

describe('/api/invites/token/[token] - GET', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default to authenticated user
    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
  });

  const mockInvitation = {
    id: 'invite-1',
    email: '<EMAIL>',
    status: 'PENDING',
    workspace: {
      id: 'workspace-1',
      name: 'Test Workspace',
      description: 'A test workspace',
    },
    role: {
      id: 'role-1',
      name: 'Member',
    },
    invitedByUser: {
      id: 'user-1',
      email: '<EMAIL>',
      displayName: 'John Doe',
    },
    createdAt: new Date('2024-01-01T00:00:00Z'),
  };

  it('should return full invitation details for authenticated user with valid token', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockResolvedValue(mockInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toEqual({
      id: mockInvitation.id,
      email: mockInvitation.email,
      status: mockInvitation.status,
      workspace: mockInvitation.workspace,
      role: mockInvitation.role,
      invitedByUser: mockInvitation.invitedByUser,
      createdAt: mockInvitation.createdAt.toISOString(),
    });
    expect(mockDb.workspaceInvite.findFirst).toHaveBeenCalledWith({
      where: { token: hashToken(validToken) },
      include: {
        workspace: { select: { id: true, name: true } },
        role: { select: { id: true, name: true } },
        invitedByUser: { select: { id: true, email: true, displayName: true } },
      },
    });
  });

  it('should return limited invitation details for unauthenticated user with valid token', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockResolvedValue(mockInvitation as any);

    // Mock unauthenticated user
    mockIsUserAuthenticated.mockResolvedValue({
      ok: false,
      uid: null,
      email: null,
    } as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toEqual({
      id: mockInvitation.id,
      status: mockInvitation.status,
      workspace: mockInvitation.workspace,
      createdAt: mockInvitation.createdAt.toISOString(),
    });
    // Should not include email, role, or invitedByUser for unauthenticated users
    expect(data.email).toBeUndefined();
    expect(data.role).toBeUndefined();
    expect(data.invitedByUser).toBeUndefined();
  });

  it('should return 400 for invalid token format', async () => {
    const invalidToken = 'invalid-token'; // This will fail the real isValidToken validation

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: invalidToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Invalid token format');
    expect(mockDb.workspaceInvite.findFirst).not.toHaveBeenCalled();
  });

  it('should return 404 for non-existent invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(404);
    expect(data.error).toBe('Invitation not found or expired');
    expect(mockDb.workspaceInvite.findFirst).toHaveBeenCalled();
  });

  it('should return 410 for already accepted invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const acceptedInvitation = { ...mockInvitation, status: 'ACCEPTED' };
    mockDb.workspaceInvite.findFirst.mockResolvedValue(acceptedInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(410);
    expect(data.error).toBe('Invitation already processed');
    expect(data.status).toBe('ACCEPTED');
  });

  it('should return 410 for already rejected invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const rejectedInvitation = { ...mockInvitation, status: 'REJECTED' };
    mockDb.workspaceInvite.findFirst.mockResolvedValue(rejectedInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(410);
    expect(data.error).toBe('Invitation already processed');
    expect(data.status).toBe('REJECTED');
  });

  it('should handle database errors', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockRejectedValue(new Error('Database error'));

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to retrieve invitation');
  });

  it('should handle authentication errors gracefully', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    mockDb.workspaceInvite.findFirst.mockResolvedValue(mockInvitation as any);

    // Mock authentication failure
    mockIsUserAuthenticated.mockRejectedValue(new Error('Auth error'));

    const request = new NextRequest('http://localhost:3000/api/invites/token/test');
    const params = Promise.resolve({ token: validToken });

    const response = await GET(request, { params });
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to retrieve invitation');
  });
});
