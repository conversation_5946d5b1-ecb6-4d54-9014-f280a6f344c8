import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { hashToken, isValidToken } from '@/lib/utils/token';
import { db } from '@/services/db';
import { InviteStatus } from '@prisma/client';

/**
 * @swagger
 * /api/invites/token/{token}:
 *   get:
 *     summary: Get invitation details by token
 *     description: Retrieve invitation details using the invitation token from email links. Email, role and invitedByUser are only included for authenticated users.
 *     tags:
 *       - Invitations
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation token
 *     responses:
 *       200:
 *         description: Invitation details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 email:
 *                   type: string
 *                   description: Only included for authenticated users
 *                 status:
 *                   type: string
 *                   enum: [PENDING, ACCEPTED, REJECTED]
 *                 workspace:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                 role:
 *                   type: object
 *                   description: Only included for authenticated users
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                 invitedByUser:
 *                   type: object
 *                   description: Only included for authenticated users
 *                   properties:
 *                     id:
 *                       type: string
 *                     email:
 *                       type: string
 *                     displayName:
 *                       type: string
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid token format
 *       404:
 *         description: Invitation not found or expired
 *       410:
 *         description: Invitation already accepted or rejected
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
): Promise<NextResponse> {
  try {
    // Get the authenticated user from Firebase
    const { ok: isAuthenticated } = await isUserAuthenticated();

    const { token } = await params;

    // Validate token format
    if (!isValidToken(token)) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 400 });
    }

    // Find the invitation by token (hash the token before comparing)
    const invitation = await db.workspaceInvite.findFirst({
      where: {
        token: hashToken(token),
      },
      include: {
        workspace: {
          select: {
            id: true,
            name: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
        invitedByUser: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found or expired' }, { status: 404 });
    }

    // Check if invitation is still pending
    if (invitation.status !== InviteStatus.PENDING) {
      return NextResponse.json(
        {
          error: 'Invitation already processed',
          status: invitation.status,
        },
        { status: 410 }
      );
    }

    // Return invitation details (exclude sensitive information for unauthenticated users)
    const response: any = {
      id: invitation.id,
      status: invitation.status,
      workspace: invitation.workspace,
      createdAt: invitation.createdAt,
    };

    // Only include email, role and invitedByUser for authenticated users
    if (isAuthenticated) {
      response.email = invitation.email;
      response.role = invitation.role;
      response.invitedByUser = invitation.invitedByUser;
    }

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Error retrieving invitation by token:', error);
    return NextResponse.json({ error: 'Failed to retrieve invitation' }, { status: 500 });
  }
}
