import 'server-only';

import { InviteStatus, Permission } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { forbiddenResponse, unauthorizedResponse } from '@/lib/auth/responses';
import { isValidToken } from '@/lib/utils/token';
import { db } from '@/services/db';

/**
 * @swagger
 * /api/workspace/invites/{inviteId}:
 *   delete:
 *     summary: Cancel/revoke workspace invitation
 *     description: Cancel or revoke a workspace invitation. Only the inviter or users with MANAGE_INVITATIONS permission can cancel invitations.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: inviteId
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation ID
 *     responses:
 *       200:
 *         description: Invitation cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invitation cancelled successfully"
 *       400:
 *         description: Bad request - invitation not found or cannot be cancelled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot cancel this invitation
 *       500:
 *         description: Internal server error
 */
export async function DELETE(_request: NextRequest, props: { params: Promise<{ inviteId: string }> }) {
  const params = await props.params;
  try {
    const { inviteId } = params;

    if (!isValidToken(inviteId)) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 400 });
    }

    const { ok, uid } = await isUserAuthenticated();

    if (!ok) {
      return unauthorizedResponse();
    }

    // Get the invitation
    const invitation = await db.workspaceInvite.findUnique({
      where: {
        id: inviteId,
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found' }, { status: 400 });
    }

    // Check if invitation can be cancelled (only pending invitations)
    if (invitation.status !== InviteStatus.PENDING) {
      return NextResponse.json({ error: 'Only pending invitations can be cancelled' }, { status: 400 });
    }

    let canCancel = false;

    // Check if user has MANAGE_INVITATIONS permission in the workspace
    try {
      const membership = await db.workspaceMembership.findFirst({
        where: {
          userId: uid,
          workspaceId: invitation.workspaceId!,
          role: {
            permissions: {
              some: {
                id: Permission.MANAGE_INVITATIONS,
              },
            },
          },
        },
      });

      canCancel = !!membership;
    } catch (error) {
      console.error('Error checking permissions:', error);
      canCancel = false;
    }

    if (!canCancel) {
      return forbiddenResponse([Permission.MANAGE_INVITATIONS]);
    }

    // Delete the invitation
    await db.workspaceInvite.delete({
      where: { id: inviteId },
    });

    return NextResponse.json({
      message: 'Invitation cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling workspace invitation:', error);
    return NextResponse.json({ error: 'Failed to cancel invitation' }, { status: 500 });
  }
}
