import { CoreApiService } from '../core';
import { UserApiService } from '../user';

// Mock the CoreApiService
jest.mock('../core');

describe('WorkspaceApiService', () => {
  let mockApiService: jest.Mocked<CoreApiService>;
  let userApiService: UserApiService;

  beforeEach(() => {
    // Create a mock CoreApiService
    mockApiService = {
      request: jest.fn(),
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    } as unknown as jest.Mocked<CoreApiService>;

    // Create a new UserApiService with the mock
    userApiService = new UserApiService(mockApiService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockInvitation = {
    id: 'invite-1',
    email: '<EMAIL>',
    workspaceId: 'workspace-1',
    invitedBy: 'user-1',
    userId: null,
    roleId: 'member',
    status: 'PENDING',
    createdAt: new Date(),
    updatedAt: new Date(),
    token: null,
    invitedByUser: { id: 'user-1', email: '<EMAIL>', displayName: 'Inviter' },
    user: null,
    role: { id: 'member', name: 'Member' },
  };

  const mockWorkspace = {
    id: 'workspace-1',
    name: 'Test Workspace',
    avatar: null,
  };

  describe('getUserInvitations', () => {
    it('should get user invitations', async () => {
      const userInvitation = { ...mockInvitation, workspace: mockWorkspace };
      mockApiService.request.mockResolvedValue([userInvitation]);

      const result = await userApiService.getUserInvitations();

      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'GET',
        url: '/user/invites',
      });
      expect(result).toEqual([userInvitation]);
    });
  });
});
