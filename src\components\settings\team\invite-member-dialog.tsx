'use client';

import Alert from '@mui/material/Alert';
import Autocomplete from '@mui/material/Autocomplete';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Snackbar from '@mui/material/Snackbar';
import TextField from '@mui/material/TextField';

import { Role } from '@prisma/client';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { getTranslatedRole } from '@/lib/models/role';
import { zodResolver } from '@hookform/resolvers/zod';

// Form validation schema
const inviteSchema = zod.object({
  email: zod.string().email('Invalid email address'),
  roleId: zod.string().min(1, 'Role is required'),
});

type InviteFormData = zod.infer<typeof inviteSchema>;

interface InviteMemberDialogProps {
  open: boolean;
  onCloseAction: () => void;
  onInviteAction: (_email: string, _roleId: string) => Promise<void>;
  roles: Role[];
  loading?: boolean;
}

export function InviteMemberDialog({
  open,
  onCloseAction,
  onInviteAction,
  roles,
  loading = false,
}: InviteMemberDialogProps) {
  const roleTranslations = useTranslations('settings.team');
  const t = useTranslations('settings.team');

  // Error state for snackbar
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);
  const [successMessage, setSuccessMessage] = React.useState<string | null>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: '',
      roleId: '',
    },
  });

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      setErrorMessage(null);
      setSuccessMessage(null);
      onCloseAction();
    }
  };

  const onSubmit = async (data: InviteFormData) => {
    try {
      setErrorMessage(null);
      setSuccessMessage(null);

      await onInviteAction(data.email, data.roleId);

      setSuccessMessage(t('inviteSuccess'));
      reset();

      // Close dialog after a short delay to show success message
      setTimeout(() => {
        setSuccessMessage(null);
        onCloseAction();
      }, 2000);
    } catch (error: any) {
      console.error('Error inviting member:', error);

      // Parse error response to get specific error message
      let errorMsg = t('inviteError'); // Default error message

      if (error?.response?.data) {
        const { userAlreadyOnWorkspace, userAlreadyInvited } = error.response.data;

        if (userAlreadyOnWorkspace) {
          errorMsg = t('inviteErrorUserAlreadyMember');
        } else if (userAlreadyInvited) {
          errorMsg = t('inviteErrorUserAlreadyInvited');
        }
      }

      setErrorMessage(errorMsg);
    }
  };

  const handleCloseSnackbar = () => {
    setErrorMessage(null);
    setSuccessMessage(null);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth='sm' fullWidth>
      <DialogTitle>{t('inviteMember')}</DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          <Controller
            name='email'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label={t('email')}
                type='email'
                fullWidth
                margin='normal'
                error={!!errors.email}
                helperText={errors.email?.message}
                disabled={isSubmitting}
                autoFocus
              />
            )}
          />

          <Controller
            name='roleId'
            control={control}
            render={({ field: { onChange, value, ...field } }) => (
              <Autocomplete
                {...field}
                options={roles}
                getOptionLabel={(option) => getTranslatedRole(option, roleTranslations)}
                value={roles.find((role) => role.id === value) || null}
                onChange={(_, newValue) => onChange(newValue?.id || '')}
                disabled={isSubmitting || loading}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('role')}
                    margin='normal'
                    fullWidth
                    error={!!errors.roleId}
                    helperText={errors.roleId?.message}
                  />
                )}
                isOptionEqualToValue={(option, roleValue) => option.id === roleValue.id}
              />
            )}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={isSubmitting}>
            {t('cancel')}
          </Button>
          <Button type='submit' variant='contained' disabled={isSubmitting}>
            {isSubmitting ? t('inviting') : t('sendInvite')}
          </Button>
        </DialogActions>
      </form>

      {/* Error Snackbar */}
      <Snackbar
        open={!!errorMessage}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity='error' sx={{ width: '100%' }}>
          {errorMessage}
        </Alert>
      </Snackbar>

      {/* Success Snackbar */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={2000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity='success' sx={{ width: '100%' }}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Dialog>
  );
}
