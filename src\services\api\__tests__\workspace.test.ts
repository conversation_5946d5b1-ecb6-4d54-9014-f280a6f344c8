import { CoreApiService } from '../core';
import { WorkspaceApiService } from '../workspace';

// Mock the CoreApiService
jest.mock('../core');

describe('WorkspaceApiService', () => {
  let mockApiService: jest.Mocked<CoreApiService>;
  let workspaceApiService: WorkspaceApiService;

  beforeEach(() => {
    // Create a mock CoreApiService
    mockApiService = {
      request: jest.fn(),
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
    } as unknown as jest.Mocked<CoreApiService>;

    // Create a new WorkspaceApiService with the mock
    workspaceApiService = new WorkspaceApiService(mockApiService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Invitation-related tests
  describe('Invitation methods', () => {
    const mockInvitation = {
      id: 'invite-1',
      email: '<EMAIL>',
      workspaceId: 'workspace-1',
      invitedBy: 'user-1',
      userId: null,
      roleId: 'member',
      status: 'PENDING',
      createdAt: new Date(),
      updatedAt: new Date(),
      token: null,
      invitedByUser: { id: 'user-1', email: '<EMAIL>', displayName: 'Inviter' },
      user: null,
      role: { id: 'member', name: 'Member' },
    };

    describe('createInvitation', () => {
      it('should create workspace invitation', async () => {
        const email = '<EMAIL>';
        const roleId = 'member';
        mockApiService.request.mockResolvedValue(mockInvitation);

        const result = await workspaceApiService.createInvitation('workspace-1', email, roleId);

        expect(mockApiService.request).toHaveBeenCalledWith({
          method: 'POST',
          url: '/workspace/workspace-1/invites',
          data: { email, roleId },
        });
        expect(result).toEqual(mockInvitation);
      });
    });

    describe('getWorkspaceInvitations', () => {
      it('should get workspace invitations', async () => {
        mockApiService.request.mockResolvedValue([mockInvitation]);

        const result = await workspaceApiService.getWorkspaceInvitations('workspace-1');

        expect(mockApiService.request).toHaveBeenCalledWith({
          method: 'GET',
          url: '/workspace/workspace-1/invites',
        });
        expect(result).toEqual([mockInvitation]);
      });
    });

    describe('acceptInvitation', () => {
      it('should accept invitation', async () => {
        const acceptResponse = { message: 'Invitation accepted', membership: {} };
        mockApiService.request.mockResolvedValue(acceptResponse);

        const result = await workspaceApiService.acceptInvitation('invite-1');

        expect(mockApiService.request).toHaveBeenCalledWith({
          method: 'PATCH',
          url: '/workspace/invites/invite-1/accept',
        });
        expect(result).toEqual(acceptResponse);
      });
    });

    describe('declineInvitation', () => {
      it('should decline invitation', async () => {
        const declineResponse = { message: 'Invitation declined', invitation: mockInvitation };
        mockApiService.request.mockResolvedValue(declineResponse);

        const result = await workspaceApiService.declineInvitation('invite-1');

        expect(mockApiService.request).toHaveBeenCalledWith({
          method: 'PATCH',
          url: '/workspace/invites/invite-1/decline',
        });
        expect(result).toEqual(declineResponse);
      });
    });

    describe('cancelInvitation', () => {
      it('should cancel invitation', async () => {
        const cancelResponse = { message: 'Invitation cancelled successfully' };
        mockApiService.request.mockResolvedValue(cancelResponse);

        const result = await workspaceApiService.cancelInvitation('invite-1');

        expect(mockApiService.request).toHaveBeenCalledWith({
          method: 'DELETE',
          url: '/workspace/invites/invite-1',
        });
        expect(result).toEqual(cancelResponse);
      });
    });
  });

  describe('getWorkspaces', () => {
    it('should call the API with the correct parameters', async () => {
      // Mock response data
      const mockWorkspaces = [
        { id: '1', name: 'Workspace 1' },
        { id: '2', name: 'Workspace 2' },
      ];

      // Set up the mock to return the mock data
      mockApiService.request.mockResolvedValueOnce(mockWorkspaces);

      // Call the method
      const result = await workspaceApiService.getWorkspaces();

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'GET',
        url: '/workspace',
      });

      // Verify the result
      expect(result).toEqual(mockWorkspaces);
    });
  });

  describe('getWorkspace', () => {
    it('should call the API with the correct parameters', async () => {
      // Mock response data
      const mockWorkspace = { id: '1', name: 'Workspace 1' };

      // Set up the mock to return the mock data
      mockApiService.request.mockResolvedValueOnce(mockWorkspace);

      // Call the method
      const result = await workspaceApiService.getWorkspace('1');

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'GET',
        url: '/workspace/1',
      });

      // Verify the result
      expect(result).toEqual(mockWorkspace);
    });
  });

  describe('createWorkspace', () => {
    it('should call the API with the correct parameters', async () => {
      // Mock response data
      const mockWorkspace = { id: '1', name: 'New Workspace' };
      const workspaceData = { name: 'New Workspace' };

      // Set up the mock to return the mock data
      mockApiService.request.mockResolvedValueOnce(mockWorkspace);

      // Call the method
      const result = await workspaceApiService.createWorkspace(workspaceData);

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'POST',
        url: '/workspace',
        data: workspaceData,
      });

      // Verify the result
      expect(result).toEqual(mockWorkspace);
    });
  });

  describe('updateWorkspace', () => {
    it('should call the API with the correct parameters', async () => {
      // Mock response data
      const mockWorkspace = { id: '1', name: 'Updated Workspace' };
      const updateData = { name: 'Updated Workspace' };

      // Set up the mock to return the mock data
      mockApiService.request.mockResolvedValueOnce(mockWorkspace);

      // Call the method
      const result = await workspaceApiService.updateWorkspace('1', updateData);

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'PATCH',
        url: '/workspace/1',
        data: updateData,
      });

      // Verify the result
      expect(result).toEqual(mockWorkspace);
    });
  });

  describe('deleteWorkspace', () => {
    it('should call the API with the correct parameters', async () => {
      // Set up the mock
      mockApiService.request.mockResolvedValueOnce(undefined);

      // Call the method
      await workspaceApiService.deleteWorkspace('1');

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'DELETE',
        url: '/workspace/1',
      });
    });
  });

  describe('selectWorkspace', () => {
    it('should call the API with the correct parameters', async () => {
      // Mock response data
      const mockWorkspace = { id: '1', name: 'Selected Workspace' };

      // Set up the mock to return the mock data
      mockApiService.request.mockResolvedValueOnce(mockWorkspace);

      // Call the method
      const result = await workspaceApiService.selectWorkspace('1');

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'POST',
        url: '/workspace/1/select',
      });

      // Verify the result
      expect(result).toEqual(mockWorkspace);
    });
  });

  describe('getSelectedWorkspace', () => {
    it('should return the selected workspace when one is selected', async () => {
      // Mock response data
      const mockWorkspace = { id: '1', name: 'Selected Workspace' };

      // Set up the mock to return the mock data
      mockApiService.request.mockResolvedValueOnce(mockWorkspace);

      // Call the method
      const result = await workspaceApiService.getSelectedWorkspace();

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'GET',
        url: '/workspace/selected',
      });

      // Verify the result
      expect(result).toEqual(mockWorkspace);
    });

    it('should return null when no workspace is selected', async () => {
      // Set up the mock to throw a 404 error
      const error = new Error('Not found');
      (error as any).response = { status: 404 };
      mockApiService.request.mockRejectedValueOnce(error);

      // Call the method
      const result = await workspaceApiService.getSelectedWorkspace();

      // Verify the API was called correctly
      expect(mockApiService.request).toHaveBeenCalledWith({
        method: 'GET',
        url: '/workspace/selected',
      });

      // Verify the result
      expect(result).toBeNull();
    });

    it('should throw other errors', async () => {
      // Set up the mock to throw a non-404 error
      const error = new Error('Server error');
      (error as any).response = { status: 500 };
      mockApiService.request.mockRejectedValueOnce(error);

      // Call the method and expect it to throw
      await expect(workspaceApiService.getSelectedWorkspace()).rejects.toThrow('Server error');
    });
  });
});
