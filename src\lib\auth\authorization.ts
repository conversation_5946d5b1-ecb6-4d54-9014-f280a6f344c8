import { db } from '@/services/db';
import { Permission } from '@prisma/client';

// TODO: cache this function later so we don't hit the database on every request
// TODO: cache must be invalidated when any role/permission/workspace membership is updated
// TODO: must use redis or any distributed cache

/**
 * Check if a user has a specific permission in a workspace
 *
 * This function should be used on route.ts files to check if the user has a specific permission.
 *
 * @param uid The user's id
 * @param workspaceId The workspace's id
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export async function checkUserPermission(uid: string, workspaceId: string, permission: Permission): Promise<boolean> {
  try {
    const membership = await db.workspaceMembership.findFirst({
      where: {
        userId: uid,
        workspaceId: workspaceId,
        role: {
          permissions: {
            some: {
              id: permission,
            },
          },
        },
      },
      select: {
        userId: true,
      },
    });

    return !!membership && membership.userId === uid;
  } catch (error) {
    console.error('Error checking permissions:', error);

    return false;
  }
}
