'use client';

import { useRouter } from '@/i18n/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import Alert from '@mui/material/Alert';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Drawer from '@mui/material/Drawer';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import OutlinedInput from '@mui/material/OutlinedInput';
import Pagination from '@mui/material/Pagination';
import Stack from '@mui/material/Stack';

import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { BuildingsIcon } from '@phosphor-icons/react/dist/ssr/Buildings';
import { CheckIcon } from '@phosphor-icons/react/dist/ssr/Check';
import { DotsThreeVerticalIcon } from '@phosphor-icons/react/dist/ssr/DotsThreeVertical';
import { EnvelopeIcon } from '@phosphor-icons/react/dist/ssr/Envelope';
import { MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';
import { PencilSimpleIcon } from '@phosphor-icons/react/dist/ssr/PencilSimple';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import { TrashIcon } from '@phosphor-icons/react/dist/ssr/Trash';
import { XIcon } from '@phosphor-icons/react/dist/ssr/X';
import { useFormatter, useTranslations } from 'next-intl';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { useWorkspace } from '@/contexts/workspace-context';
import { useApiServices } from '@/hooks/use-api-services';
import { logger } from '@/lib/logger/default-logger';
import { getTranslatedRole } from '@/lib/models/role';
import { paths } from '@/paths';
import { Role, User, Workspace, WorkspaceInvite } from '@prisma/client';
import { useSearchParams } from 'next/navigation';

type PendingInviteWithDetails = WorkspaceInvite & {
  workspace: Workspace;
  invitedByUser: User;
  role: Role | null;
};

interface CreateWorkspaceFormData {
  name: string;
}

// Constants for pagination and search
const WORKSPACES_PER_PAGE = 12;
const SEARCH_THRESHOLD = 11;

// TODO: when workspace guard redirects to the workspace selection, save the url to returnTo.
// TODO: after selecting the workspace, redirect to the returnTo.
export function WorkspaceSelection(): React.JSX.Element {
  const roleTranslation = useTranslations('settings.team');
  const t = useTranslations('workspace');
  const router = useRouter();
  const {
    workspaces,
    fetchWorkspaces,
    loading,
    error,
    currentWorkspace,
    selectWorkspace,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    clearError,
  } = useWorkspace();

  const formatter = useFormatter();
  const { workspaceApiService, userApiService } = useApiServices();
  const params = useSearchParams();

  // Invitations state
  const [invitations, setInvitations] = React.useState<PendingInviteWithDetails[]>([]);
  const [invitationsLoading, setInvitationsLoading] = React.useState(true);
  const [invitationsError, setInvitationsError] = React.useState<string | null>(null);
  const [acceptingInvites, setAcceptingInvites] = React.useState<Set<string>>(new Set());
  const [decliningInvites, setDecliningInvites] = React.useState<Set<string>>(new Set());
  const [invitationsDrawerOpen, setInvitationsDrawerOpen] = React.useState(false);

  // Workspace management state
  const [isCreating, setIsCreating] = React.useState(false);
  const [isEditing, setIsEditing] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [isSelecting, setIsSelecting] = React.useState<string | null>(null);
  const [pendingNavigation, setPendingNavigation] = React.useState(false);
  const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
  const [editDialogOpen, setEditDialogOpen] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [selectedWorkspace, setSelectedWorkspace] = React.useState<any>(null);
  const [editWorkspaceName, setEditWorkspaceName] = React.useState('');
  const [editError, setEditError] = React.useState<string | null>(null);
  const [deleteError, setDeleteError] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [currentPage, setCurrentPage] = React.useState(1);
  const [menuAnchor, setMenuAnchor] = React.useState<null | HTMLElement>(null);
  const [menuWorkspaceId, setMenuWorkspaceId] = React.useState<string | null>(null);
  const [highlightedWorkspaceId, setHighlightedWorkspaceId] = React.useState<string | null>(null);

  // Load pending invitations
  const loadInvitations = React.useCallback(async () => {
    try {
      setInvitationsLoading(true);
      setInvitationsError(null);
      const data = await userApiService.getUserInvitations();
      setInvitations(data);
    } catch (err) {
      console.error('Error loading invitations:', err);
      setInvitationsError(t('invitations.errors.listLoadFailed'));
    } finally {
      setInvitationsLoading(false);
    }
  }, [userApiService, t]);

  // Load invitations on mount
  React.useEffect(() => {
    loadInvitations();
  }, [loadInvitations]);

  // Handle accepting invitation
  const handleAcceptInvitation = async (inviteId: string) => {
    try {
      setAcceptingInvites((prev) => new Set(prev).add(inviteId));
      await workspaceApiService.acceptInvitation(inviteId);

      // Remove the invitation from the list
      setInvitations((prev) => prev.filter((inv) => inv.id !== inviteId));

      // Refresh workspaces when an invitation is accepted
      fetchWorkspaces();
    } catch (err) {
      console.error('Error accepting invitation:', err);
      setInvitationsError(t('invitations.errors.acceptFailed'));
    } finally {
      setAcceptingInvites((prev) => {
        const newSet = new Set(prev);
        newSet.delete(inviteId);
        return newSet;
      });
    }
  };

  // Handle declining invitation
  const handleDeclineInvitation = async (inviteId: string) => {
    try {
      setDecliningInvites((prev) => new Set(prev).add(inviteId));
      await workspaceApiService.declineInvitation(inviteId);

      // Remove the invitation from the list
      setInvitations((prev) => prev.filter((inv) => inv.id !== inviteId));
    } catch (err) {
      console.error('Error declining invitation:', err);
      setInvitationsError(t('invitations.errors.declineFailed'));
    } finally {
      setDecliningInvites((prev) => {
        const newSet = new Set(prev);
        newSet.delete(inviteId);
        return newSet;
      });
    }
  };

  // Create schema with translations
  const schema = React.useMemo(() => {
    return zod.object({
      name: zod.string().min(1, { message: t('selection.workspaceNameRequired') }),
    });
  }, [t]);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateWorkspaceFormData>({
    defaultValues: { name: '' },
    resolver: zodResolver(schema),
  });

  // Filter workspaces based on search query
  const filteredWorkspaces = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return workspaces;
    }
    return workspaces.filter((workspace) => workspace.name?.toLowerCase().includes(searchQuery.toLowerCase()));
  }, [workspaces, searchQuery]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredWorkspaces.length / WORKSPACES_PER_PAGE);
  const startIndex = (currentPage - 1) * WORKSPACES_PER_PAGE;
  const endIndex = startIndex + WORKSPACES_PER_PAGE;
  const paginatedWorkspaces = filteredWorkspaces.slice(startIndex, endIndex);

  // Show search and pagination if there are enough workspaces or if there's a workspaceName param
  const showSearchAndPagination = workspaces.length > SEARCH_THRESHOLD || params.has('workspaceName');

  // Reset page when search changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  // Handle query parameters
  React.useEffect(() => {
    // Handle workspaceName parameter - set search query
    const nameParam = params.get('workspaceName');
    if (nameParam) {
      setSearchQuery(nameParam);
    }

    // Handle workspaceId parameter - highlight card
    const idParam = params.get('workspaceId');
    if (idParam) {
      setHighlightedWorkspaceId(idParam);
    }
  }, [params]); // Only run when params change

  // Handle navigation after workspace selection
  React.useEffect(() => {
    logger.debug('[WorkspaceSelection]: Navigation effect triggered', {
      pendingNavigation,
      currentWorkspace: currentWorkspace?.id,
      loading,
      shouldNavigate: pendingNavigation && currentWorkspace && !loading,
    });

    if (pendingNavigation && currentWorkspace && !loading) {
      logger.debug('[WorkspaceSelection]: Navigating to home page');
      setPendingNavigation(false);

      // Add a small delay to ensure all state updates are complete
      setTimeout(() => {
        logger.debug('[WorkspaceSelection]: Actually navigating now');

        const returnTo = params.get('returnTo') ?? '';

        logger.debug(`[WorkspaceSelection]: Redirecting to ${returnTo ? 'returnTo' : paths.root}`, {
          returnTo,
        });

        if (returnTo) {
          router.push(returnTo as any);
          return;
        }

        router.push(paths.root);
      }, 100);
    }
  }, [pendingNavigation, currentWorkspace, loading, router, params]);

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, workspaceId: string) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
    setMenuWorkspaceId(workspaceId);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setMenuWorkspaceId(null);
  };

  const handleEditWorkspace = () => {
    const workspace = workspaces.find((w) => w.id === menuWorkspaceId);
    if (workspace) {
      setSelectedWorkspace(workspace);
      setEditWorkspaceName(workspace.name || '');
      setEditError(null); // Clear any previous errors
      setEditDialogOpen(true);
    }
    handleMenuClose();
  };

  const handleDeleteWorkspace = () => {
    const workspace = workspaces.find((w) => w.id === menuWorkspaceId);
    if (workspace) {
      setSelectedWorkspace(workspace);
      setDeleteError(null); // Clear any previous errors
      setDeleteDialogOpen(true);
    }
    handleMenuClose();
  };

  // Save workspace changes
  const handleSaveWorkspace = async () => {
    if (!selectedWorkspace || !editWorkspaceName.trim()) return;

    try {
      setIsEditing(true);
      setEditError(null);
      clearError(); // Clear any context errors
      await updateWorkspace(selectedWorkspace.id, { name: editWorkspaceName.trim() });
      setEditDialogOpen(false);
      setSelectedWorkspace(null);
      setEditWorkspaceName('');
    } catch (updateError) {
      console.error('Error updating workspace:', updateError);
      setEditError(t('selection.errors.editFailed'));
    } finally {
      setIsEditing(false);
    }
  };

  // Confirm delete workspace
  const handleConfirmDelete = async () => {
    if (!selectedWorkspace) return;

    try {
      setIsDeleting(true);
      setDeleteError(null);
      clearError(); // Clear any context errors
      await deleteWorkspace(selectedWorkspace.id);
      setDeleteDialogOpen(false);
      setSelectedWorkspace(null);
    } catch (deleteErr) {
      console.error('Error deleting workspace:', deleteErr);
      setDeleteError(t('selection.errors.deleteFailed'));
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle workspace selection
  const handleSelectWorkspace = React.useCallback(
    async (workspaceId: string) => {
      logger.debug('[WorkspaceSelection]: Starting workspace selection', { workspaceId });

      try {
        setIsSelecting(workspaceId);
        setPendingNavigation(true);
        logger.debug('[WorkspaceSelection]: Calling selectWorkspace API');
        await selectWorkspace(workspaceId);
        logger.debug('[WorkspaceSelection]: selectWorkspace API completed successfully');
      } catch (selectError) {
        console.error('[WorkspaceSelection]: Error selecting workspace:', selectError);
        setPendingNavigation(false);
        setIsSelecting(null);
      }
    },
    [selectWorkspace]
  );

  // Handle workspace creation
  const handleCreateWorkspace = React.useCallback(
    async (data: CreateWorkspaceFormData) => {
      try {
        setIsCreating(true);
        await createWorkspace({ name: data.name });
        setCreateDialogOpen(false);
        reset();
      } catch (createError) {
        console.error('Error creating workspace:', createError);
        // Error is handled by the context
      } finally {
        setIsCreating(false);
      }
    },
    [createWorkspace, reset]
  );

  // Handle dialog close
  const handleCloseDialog = React.useCallback(() => {
    setCreateDialogOpen(false);
    reset();
    clearError();
  }, [reset, clearError]);

  // Render loading state
  if (loading && workspaces.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '60vh',
          gap: 2,
        }}
      >
        <CircularProgress />
        <Typography variant='body2' color='text.secondary'>
          {t('selection.loadingWorkspaces')}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Stack spacing={4}>
        {/* Header */}
        <Stack spacing={2} alignItems='center' textAlign='center'>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: (theme) => `${theme.palette.primary.main}15`,
              color: 'primary.main',
              borderRadius: '50%',
              p: 2,
              width: 80,
              height: 80,
            }}
          >
            <BuildingsIcon fontSize='var(--icon-fontSize-xl)' />
          </Box>
          <Typography variant='h4' component='h1'>
            {t('selection.title')}
          </Typography>
          <Typography variant='body1' color='text.secondary' sx={{ maxWidth: 600 }}>
            {t('selection.description')}
          </Typography>
        </Stack>

        {/* Error Alert - Only show if no modals are open */}
        {error && !createDialogOpen && !editDialogOpen && !deleteDialogOpen && (
          <Alert severity='error' onClose={clearError}>
            {error.message}
          </Alert>
        )}

        {/* Invitations Card - Show when there are pending invitations */}
        {!invitationsLoading && invitations.length > 0 && (
          <Card
            sx={{
              cursor: 'pointer',
              transition: 'all 0.2s',
              border: '1px solid',
              borderColor: 'primary.main',
              bgcolor: (theme) => `${theme.palette.primary.main}08`,
              '&:hover': {
                borderColor: 'primary.main',
                bgcolor: (theme) => `${theme.palette.primary.main}12`,
                transform: 'translateY(-1px)',
                boxShadow: (theme) => theme.shadows[2],
              },
            }}
            onClick={() => setInvitationsDrawerOpen(true)}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar
                  sx={{
                    width: 48,
                    height: 48,
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                  }}
                >
                  <EnvelopeIcon size={24} />
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant='h6'
                    component='h3'
                    sx={{
                      fontWeight: 600,
                      color: 'primary.main',
                      mb: 0.5,
                    }}
                  >
                    {t('selection.invitationsCard.title')}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    {t('selection.invitationsCard.description', { count: invitations.length })}
                  </Typography>
                </Box>
                <Typography
                  variant='body2'
                  sx={{
                    color: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                  }}
                >
                  {t('selection.invitationsCard.viewInvitations')}
                  <EnvelopeIcon size={16} />
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Workspaces Section */}
        <Box>
          {/* Action Bar with Search and Create Button - Only show when pagination is needed */}
          {showSearchAndPagination && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                flexWrap: 'wrap',
                gap: 2,
              }}
            >
              {/* Search Bar */}
              <TextField
                placeholder={t('selection.searchWorkspaces')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position='start'>
                        <MagnifyingGlassIcon />
                      </InputAdornment>
                    ),
                  },
                }}
                sx={{ minWidth: 300, maxWidth: 400 }}
              />

              {/* Create New Workspace Button */}
              <Button
                variant='contained'
                size='large'
                startIcon={<PlusIcon />}
                onClick={() => setCreateDialogOpen(true)}
                sx={{
                  px: 3,
                  py: 1.5,
                  fontSize: '1rem',
                  fontWeight: 600,
                }}
              >
                {t('selection.createWorkspace')}
              </Button>
            </Box>
          )}

          {/* Workspaces Grid */}
          {filteredWorkspaces.length > 0 ? (
            <>
              <Grid container spacing={4} justifyContent='center'>
                {paginatedWorkspaces.map((workspace) => (
                  <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={workspace.id}>
                    <Card
                      id={`workspace-${workspace.id}`}
                      sx={{
                        transition: 'all 0.2s',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        ...(highlightedWorkspaceId === workspace.id && {
                          border: '2px solid',
                          borderColor: 'primary.main',
                          boxShadow: (theme) => theme.shadows[4],
                        }),
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: (theme) => theme.shadows[4],
                          '& [data-workspace-actions]': {
                            opacity: 1,
                          },
                        },
                      }}
                    >
                      {/* Action Menu Button */}
                      <IconButton
                        data-workspace-actions
                        sx={{
                          position: 'absolute',
                          top: 12,
                          right: 12,
                          opacity: menuWorkspaceId === workspace.id ? 1 : 0,
                          transition: 'all 0.2s',
                          bgcolor: 'background.paper',
                          backdropFilter: 'blur(8px)',
                          border: '1px solid',
                          borderColor: 'divider',
                          width: 32,
                          height: 32,
                          color: 'text.primary',
                          '&:hover': {
                            bgcolor: 'action.hover',
                            borderColor: 'primary.main',
                            transform: 'scale(1.05)',
                            boxShadow: (theme) => theme.shadows[4],
                          },
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleMenuOpen(e, workspace.id);
                        }}
                        size='small'
                      >
                        <DotsThreeVerticalIcon size={16} />
                      </IconButton>

                      <CardContent
                        sx={{
                          flex: 1,
                          display: 'flex',
                          flexDirection: 'column',
                          p: 3,
                          '&:last-child': { pb: 3 },
                        }}
                      >
                        <Stack spacing={3} alignItems='center' textAlign='center' sx={{ flex: 1 }}>
                          <Avatar
                            sx={{
                              width: 72,
                              height: 72,
                              bgcolor: 'primary.main',
                              fontSize: '1.75rem',
                              fontWeight: 600,
                            }}
                            src={workspace.avatar || undefined}
                          >
                            {workspace.name?.charAt(0).toUpperCase() || 'W'}
                          </Avatar>
                          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                            <Typography
                              variant='h6'
                              component='h3'
                              sx={{
                                fontWeight: 600,
                                mb: 1,
                                lineHeight: 1.3,
                                wordBreak: 'break-word',
                              }}
                            >
                              {workspace.name || 'Unnamed Workspace'}
                            </Typography>
                          </Box>
                          <Button
                            variant='outlined'
                            size='medium'
                            fullWidth
                            disabled={isSelecting === workspace.id}
                            startIcon={isSelecting === workspace.id ? <CircularProgress size={16} /> : null}
                            sx={{ mt: 'auto' }}
                            onClick={() => handleSelectWorkspace(workspace.id)}
                          >
                            {isSelecting === workspace.id ? t('selection.selecting') : t('selection.selectWorkspace')}
                          </Button>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}

                {/* Create Workspace Card - Only show when no pagination */}
                {!showSearchAndPagination && (
                  <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        border: '2px dashed',
                        borderColor: 'divider',
                        bgcolor: 'transparent',
                        '&:hover': {
                          borderColor: 'primary.main',
                          bgcolor: (theme) => `${theme.palette.primary.main}08`,
                          transform: 'translateY(-2px)',
                          boxShadow: (theme) => theme.shadows[4],
                        },
                      }}
                      onClick={() => setCreateDialogOpen(true)}
                    >
                      <CardContent
                        sx={{
                          flex: 1,
                          display: 'flex',
                          flexDirection: 'column',
                          p: 3,
                          '&:last-child': { pb: 3 },
                        }}
                      >
                        <Stack
                          spacing={3}
                          alignItems='center'
                          textAlign='center'
                          sx={{ flex: 1, justifyContent: 'center' }}
                        >
                          <Avatar
                            sx={{
                              width: 72,
                              height: 72,
                              bgcolor: 'primary.main',
                              fontSize: '1.75rem',
                              fontWeight: 600,
                            }}
                          >
                            <PlusIcon size={32} />
                          </Avatar>
                          <Box>
                            <Typography
                              variant='h6'
                              component='h3'
                              sx={{
                                fontWeight: 600,
                                mb: 1,
                                lineHeight: 1.3,
                              }}
                            >
                              {t('selection.createWorkspace')}
                            </Typography>
                            <Typography variant='body2' color='text.secondary'>
                              {t('selection.createWorkspaceCardDescription')}
                            </Typography>
                          </Box>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                )}
              </Grid>

              {/* Pagination */}
              {showSearchAndPagination && totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination
                    count={totalPages}
                    page={currentPage}
                    onChange={(_, page) => setCurrentPage(page)}
                    color='primary'
                  />
                </Box>
              )}
            </>
          ) : searchQuery ? (
            /* No Search Results */
            <Stack spacing={3} alignItems='center' textAlign='center'>
              <Typography variant='h6'>{t('selection.noSearchResults')}</Typography>
              <Typography variant='body2' color='text.secondary' sx={{ maxWidth: 500 }}>
                {t('selection.noSearchResultsDescription')}
              </Typography>
            </Stack>
          ) : (
            /* No Workspaces State - Show create workspace card */
            <Grid container spacing={4} justifyContent='center'>
              <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    border: '2px dashed',
                    borderColor: 'divider',
                    bgcolor: 'transparent',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: (theme) => `${theme.palette.primary.main}08`,
                      transform: 'translateY(-2px)',
                      boxShadow: (theme) => theme.shadows[4],
                    },
                  }}
                  onClick={() => setCreateDialogOpen(true)}
                >
                  <CardContent
                    sx={{
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'column',
                      p: 3,
                      '&:last-child': { pb: 3 },
                    }}
                  >
                    <Stack
                      spacing={3}
                      alignItems='center'
                      textAlign='center'
                      sx={{ flex: 1, justifyContent: 'center' }}
                    >
                      <Avatar
                        sx={{
                          width: 72,
                          height: 72,
                          bgcolor: 'primary.main',
                          fontSize: '1.75rem',
                          fontWeight: 600,
                        }}
                      >
                        <PlusIcon size={32} />
                      </Avatar>
                      <Box>
                        <Typography
                          variant='h6'
                          component='h3'
                          sx={{
                            fontWeight: 600,
                            mb: 1,
                            lineHeight: 1.3,
                          }}
                        >
                          {t('selection.createWorkspace')}
                        </Typography>
                        <Typography variant='body2' color='text.secondary'>
                          {t('selection.noWorkspacesDescription')}
                        </Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </Box>
      </Stack>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleEditWorkspace}>
          <PencilSimpleIcon style={{ marginRight: 8 }} />
          {t('selection.editWorkspace')}
        </MenuItem>
        <MenuItem onClick={handleDeleteWorkspace} sx={{ color: 'error.main' }}>
          <TrashIcon style={{ marginRight: 8 }} />
          {t('selection.deleteWorkspace')}
        </MenuItem>
      </Menu>
      {/* Create Workspace Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCloseDialog}
        maxWidth='sm'
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: (theme) => theme.shadows[10],
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Typography variant='h5' component='div' sx={{ fontWeight: 600 }}>
            {t('selection.createWorkspaceTitle')}
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
            {t('selection.createWorkspaceDescription')}
          </Typography>
        </DialogTitle>

        <form onSubmit={handleSubmit(handleCreateWorkspace)}>
          <DialogContent sx={{ pt: 3, pb: 2 }}>
            <Stack spacing={4}>
              {/* Avatar Selection Placeholder */}
              <Box>
                <Typography variant='subtitle2' sx={{ mb: 1 }}>
                  {t('selection.workspaceAvatar')}
                </Typography>
                <Stack direction='row' alignItems='center' spacing={2}>
                  <Avatar
                    sx={{
                      width: 64,
                      height: 64,
                      bgcolor: 'primary.main',
                      cursor: 'pointer',
                      border: '2px dashed',
                      borderColor: 'divider',
                      '&:hover': {
                        borderColor: 'primary.main',
                        bgcolor: 'primary.light',
                      },
                    }}
                  >
                    <PlusIcon size={24} />
                  </Avatar>
                  <Box>
                    <Typography variant='body2' color='text.secondary'>
                      {t('selection.uploadAvatarDescription')}
                    </Typography>
                    <Typography variant='caption' color='text.secondary'>
                      {t('selection.avatarRequirements')}
                    </Typography>
                  </Box>
                </Stack>
              </Box>

              {/* Workspace Name */}
              <Controller
                control={control}
                name='name'
                render={({ field }) => (
                  <FormControl fullWidth error={Boolean(errors.name)}>
                    <InputLabel>{t('selection.workspaceName')}</InputLabel>
                    <OutlinedInput
                      {...field}
                      label={t('selection.workspaceName')}
                      placeholder={t('selection.workspaceNamePlaceholder')}
                    />
                    {errors.name && <FormHelperText>{errors.name.message}</FormHelperText>}
                  </FormControl>
                )}
              />
            </Stack>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button onClick={handleCloseDialog} disabled={isCreating} size='large'>
              {t('selection.cancelButton')}
            </Button>
            <Button
              type='submit'
              variant='contained'
              disabled={isCreating}
              size='large'
              sx={{ minWidth: 120 }}
              startIcon={isCreating ? <CircularProgress size={16} /> : null}
            >
              {isCreating ? t('selection.creating') : t('selection.createButton')}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
      {/* Edit Workspace Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth='sm'
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: (theme) => theme.shadows[10],
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Typography variant='h5' component='div' sx={{ fontWeight: 600 }}>
            {t('selection.editWorkspaceTitle')}
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
            {t('selection.editWorkspaceDescription')}
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Stack spacing={4}>
            {/* Error Alert */}
            {editError && (
              <Alert severity='error' onClose={() => setEditError(null)}>
                {editError}
              </Alert>
            )}
            {/* Avatar Selection */}
            <Box>
              <Typography variant='subtitle2' sx={{ mb: 1 }}>
                {t('selection.workspaceAvatar')}
              </Typography>
              <Stack direction='row' alignItems='center' spacing={2}>
                <Avatar
                  sx={{
                    width: 64,
                    height: 64,
                    cursor: 'pointer',
                    border: '2px solid',
                    borderColor: 'divider',
                    '&:hover': {
                      borderColor: 'primary.main',
                    },
                  }}
                  src={selectedWorkspace?.avatar}
                >
                  {selectedWorkspace?.name?.charAt(0).toUpperCase() || 'W'}
                </Avatar>
                <Box>
                  <Button variant='outlined' size='small'>
                    {t('selection.changeAvatar')}
                  </Button>
                  <Typography variant='caption' display='block' color='text.secondary' sx={{ mt: 0.5 }}>
                    {t('selection.avatarRequirements')}
                  </Typography>
                </Box>
              </Stack>
            </Box>

            {/* Workspace Name */}
            <TextField
              fullWidth
              label={t('selection.workspaceName')}
              value={editWorkspaceName}
              onChange={(e) => setEditWorkspaceName(e.target.value)}
              placeholder={t('selection.workspaceNamePlaceholder')}
            />
          </Stack>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={() => setEditDialogOpen(false)} disabled={isEditing} size='large'>
            {t('selection.cancelButton')}
          </Button>
          <Button
            variant='contained'
            size='large'
            sx={{ minWidth: 120 }}
            onClick={handleSaveWorkspace}
            disabled={isEditing || !editWorkspaceName.trim()}
            startIcon={isEditing ? <CircularProgress size={16} /> : null}
          >
            {isEditing ? t('selection.saving') : t('selection.saveChanges')}
          </Button>
        </DialogActions>
      </Dialog>
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth='xs'
        fullWidth
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: (theme) => theme.shadows[10],
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Typography variant='h5' component='div' sx={{ fontWeight: 600, color: 'error.main' }}>
            {t('selection.deleteWorkspaceTitle')}
          </Typography>
          <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
            {t('selection.deleteWorkspaceDescription')}
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Stack spacing={2}>
            {/* Error Alert */}
            {deleteError && (
              <Alert severity='error' onClose={() => setDeleteError(null)}>
                {deleteError}
              </Alert>
            )}

            <Typography variant='body1'>
              {t('selection.deleteConfirmation')} <strong>"{selectedWorkspace?.name}"</strong>?
            </Typography>
            <Typography variant='body2' color='text.secondary'>
              {t('selection.deleteWarning')}
            </Typography>
          </Stack>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={isDeleting} size='large'>
            {t('selection.cancelButton')}
          </Button>
          <Button
            variant='contained'
            color='error'
            size='large'
            sx={{ minWidth: 120 }}
            onClick={handleConfirmDelete}
            disabled={isDeleting}
            startIcon={isDeleting ? <CircularProgress size={16} /> : null}
          >
            {isDeleting ? t('selection.deleting') : t('selection.deleteButton')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Invitations Drawer */}
      <Drawer
        anchor='right'
        open={invitationsDrawerOpen}
        onClose={() => setInvitationsDrawerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: 400 },
            maxWidth: '100vw',
            backgroundColor: 'var(--mui-palette-background-default)',
            backgroundImage: 'none',
          },
        }}
      >
        <Box sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Drawer Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant='h6' sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EnvelopeIcon size={20} />
              {t('invitations.drawer.title')}
            </Typography>
            <IconButton onClick={() => setInvitationsDrawerOpen(false)}>
              <XIcon size={20} />
            </IconButton>
          </Box>

          {/* Drawer Content */}
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {invitationsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : invitationsError ? (
              <Alert severity='error' sx={{ mb: 2 }}>
                {invitationsError}
              </Alert>
            ) : invitations.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <EnvelopeIcon size={48} style={{ opacity: 0.3, marginBottom: 16 }} />
                <Typography variant='body1' color='text.secondary'>
                  {t('invitations.drawer.empty')}
                </Typography>
              </Box>
            ) : (
              <Stack spacing={2}>
                {invitations.map((invitation) => (
                  <Card key={invitation.id} variant='outlined' sx={{ position: 'relative' }}>
                    {/* Date display in top left */}
                    <Typography
                      variant='caption'
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        color: 'text.secondary',
                        fontSize: '0.7rem',
                        fontWeight: 500,
                        bgcolor: 'background.paper',
                        px: 0.75,
                        py: 0.25,
                        borderRadius: 0.5,
                        border: '1px solid',
                        borderColor: 'divider',
                        zIndex: 1,
                      }}
                    >
                      {formatter.dateTime(invitation.createdAt, 'default')}
                    </Typography>
                    <CardContent sx={{ p: 2, pt: 4 }}>
                      <Stack spacing={2}>
                        {/* Workspace Info */}
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              bgcolor: 'primary.main',
                              fontSize: '1rem',
                              fontWeight: 600,
                            }}
                          >
                            {(invitation.workspace.name || 'W').charAt(0).toUpperCase()}
                          </Avatar>
                          <Box sx={{ flex: 1, minWidth: 0 }}>
                            <Typography
                              variant='subtitle1'
                              sx={{
                                fontWeight: 600,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                              }}
                            >
                              {invitation.workspace.name}
                            </Typography>
                            <Typography variant='body2' color='text.secondary'>
                              {t('invitations.invitedBy', {
                                name:
                                  invitation.invitedByUser.displayName ||
                                  invitation.invitedByUser.email ||
                                  t(`invitations.system`),
                              })}
                            </Typography>
                            {invitation.role && (
                              <Typography variant='body2' color='text.secondary'>
                                {t('invitations.invitedWithRole', {
                                  role: getTranslatedRole(invitation.role, roleTranslation),
                                })}
                              </Typography>
                            )}
                          </Box>
                        </Box>

                        {/* Action Buttons */}
                        <Stack direction='row' spacing={1}>
                          <Button
                            variant='outlined'
                            size='small'
                            fullWidth
                            disabled={acceptingInvites.has(invitation.id) || decliningInvites.has(invitation.id)}
                            startIcon={
                              acceptingInvites.has(invitation.id) ? (
                                <CircularProgress size={16} />
                              ) : (
                                <CheckIcon size={16} />
                              )
                            }
                            onClick={() => handleAcceptInvitation(invitation.id)}
                          >
                            {acceptingInvites.has(invitation.id) ? t('invitations.accepting') : t('invitations.accept')}
                          </Button>
                          <Button
                            variant='outlined'
                            color='error'
                            size='small'
                            fullWidth
                            disabled={acceptingInvites.has(invitation.id) || decliningInvites.has(invitation.id)}
                            startIcon={
                              decliningInvites.has(invitation.id) ? <CircularProgress size={16} /> : <XIcon size={16} />
                            }
                            onClick={() => handleDeclineInvitation(invitation.id)}
                          >
                            {decliningInvites.has(invitation.id)
                              ? t('invitations.declining')
                              : t('invitations.decline')}
                          </Button>
                        </Stack>
                      </Stack>
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            )}
          </Box>
        </Box>
      </Drawer>
    </Box>
  );
}
