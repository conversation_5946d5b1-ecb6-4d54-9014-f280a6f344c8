import 'server-only';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { Timezone } from 'next-intl';
import { cookies } from 'next/headers';

export async function getCurrentTimeZone(): Promise<Timezone | undefined> {
  const cookieStore = await cookies();
  const timezoneCookie = cookieStore.get('__timezone');
  let timezone = timezoneCookie?.value;

  if (timezone) {
    return timezone;
  }

  // Get the authenticated user from Firebase
  // TODO: cache this, although the cookie should always be set
  const authResult = await getAuthenticatedAppForUser();
  if (authResult.currentUser) {
    const timezoneResult = await db.user.findFirst({
      where: {
        id: authResult.currentUser.uid,
      },
      select: {
        timezone: true,
      },
    });

    if (timezoneResult?.timezone) {
      return timezoneResult.timezone;
    }
  }

  return timezone;
}
