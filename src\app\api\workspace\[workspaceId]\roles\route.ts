import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { forbiddenResponse, unauthorizedResponse } from '@/lib/auth/responses';
import { db } from '@/services/db';

/**
 * @swagger
 * /api/workspace/{workspaceId}/roles:
 *   get:
 *     summary: Get workspace roles
 *     description: Retrieve all roles available for a specific workspace. Only workspace members can access this endpoint.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     responses:
 *       200:
 *         description: Successfully retrieved workspace roles
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   workspaceId:
 *                     type: string
 *                     nullable: true
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized"
 *       403:
 *         description: Forbidden - user is not a member of the workspace
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "You do not have access to this workspace"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to get workspace roles"
 */
export async function GET(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const { ok, uid } = await isUserAuthenticated();

    // If no user is authenticated, return 401
    if (!ok) {
      return unauthorizedResponse();
    }

    // Check if the user is a member of the workspace
    const userMembership = await db.workspaceMembership.findFirst({
      where: {
        userId: uid,
        workspaceId: workspaceId,
      },
      select: {
        userId: true,
      },
    });

    if (!userMembership || !userMembership.userId) {
      return forbiddenResponse();
    }

    // Get all roles available for this workspace (workspace-specific + global roles)
    const roles = await db.role.findMany({
      where: {
        OR: [
          { workspaceId: workspaceId },
          { workspaceId: null }, // Global roles
        ],
      },
      select: {
        id: true,
        name: true,
        workspaceId: true,
      },
      orderBy: [
        { workspaceId: 'asc' }, // Global roles first
        { name: 'asc' },
      ],
    });

    return NextResponse.json(roles);
  } catch (error) {
    console.error('Error getting workspace roles:', error);
    return NextResponse.json({ error: 'Failed to get workspace roles' }, { status: 500 });
  }
}
