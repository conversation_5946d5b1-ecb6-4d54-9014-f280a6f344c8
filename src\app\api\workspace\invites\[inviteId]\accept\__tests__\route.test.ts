/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/auth/authentication', () => ({
  isUserAuthenticated: jest.fn(),
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    workspaceMembership: {
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    role: {
      findFirst: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

// Import mocked modules
import { isUserAuthenticated } from '@/lib/auth/authentication';
import { db } from '@/services/db';

import { PATCH } from '../route';

const mockIsUserAuthenticated = isUserAuthenticated as jest.MockedFunction<typeof isUserAuthenticated>;
const mockDb = db as jest.Mocked<typeof db>;

// Mock data
const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'inviter-1',
  userId: 'user-1',
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
};

const mockRole = {
  id: 'member',
  name: 'Member',
  workspaceId: 'workspace-1',
};

const mockMembership = {
  id: 'membership-1',
  userId: 'user-1',
  workspaceId: 'workspace-1',
  roleId: 'member',
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('/api/workspace/invites/[inviteId]/accept', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('PATCH', () => {
    it('should accept invitation successfully', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(null); // Not already a member
      (mockDb.role.findFirst as jest.Mock).mockResolvedValue(mockRole);

      // Mock transaction
      mockDb.$transaction.mockImplementation(async (callback) => {
        return await callback({
          workspaceInvite: {
            update: jest.fn().mockResolvedValue(mockInvitation),
          },
          workspaceMembership: {
            create: jest.fn().mockResolvedValue(mockMembership),
          },
        } as any);
      });

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation accepted successfully');
      expect(data.membership).toEqual({
        ...mockMembership,
        createdAt: mockMembership.createdAt.toISOString(),
        updatedAt: mockMembership.updatedAt.toISOString(),
      });
    });

    it('should return 401 when user is not authenticated', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: false, uid: '', email: '' });

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 400 when invitation is not found', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invitation not found');
    });

    it('should return 400 when invitation is not pending', async () => {
      const acceptedInvitation = { ...mockInvitation, status: 'ACCEPTED' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(acceptedInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invitation has already been processed');
    });

    it('should return 400 when invitation does not have roleId', async () => {
      const invitationWithoutRole = { ...mockInvitation, roleId: null };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(invitationWithoutRole);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invitation does not have a role assigned');
    });

    it('should return 400 when user is already a member', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(mockMembership); // Already a member
      (mockDb.workspaceInvite.update as jest.Mock).mockResolvedValue(mockInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('You are already a member of this workspace');
    });

    it('should return 404 when role is not found', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(null);
      (mockDb.role.findFirst as jest.Mock).mockResolvedValue(null); // Role not found

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Role not found');
    });

    it('should return 403 when user cannot accept invitation (different email and userId)', async () => {
      const differentUserInvitation = { ...mockInvitation, email: '<EMAIL>', userId: 'different-user' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(differentUserInvitation);

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });

    it('should accept invitation when user email matches invitation email', async () => {
      const emailMatchInvitation = { ...mockInvitation, userId: null, email: '<EMAIL>' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(emailMatchInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(null);
      (mockDb.role.findFirst as jest.Mock).mockResolvedValue(mockRole);

      // Mock transaction
      mockDb.$transaction.mockImplementation(async (callback) => {
        return await callback({
          workspaceInvite: {
            update: jest.fn().mockResolvedValue(mockInvitation),
          },
          workspaceMembership: {
            create: jest.fn().mockResolvedValue(mockMembership),
          },
        } as any);
      });

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation accepted successfully');
    });

    it('should accept invitation when user uid matches invitation userId', async () => {
      const userIdMatchInvitation = { ...mockInvitation, userId: 'user-1', email: '<EMAIL>' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(userIdMatchInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(null);
      (mockDb.role.findFirst as jest.Mock).mockResolvedValue(mockRole);

      // Mock transaction
      mockDb.$transaction.mockImplementation(async (callback) => {
        return await callback({
          workspaceInvite: {
            update: jest.fn().mockResolvedValue(mockInvitation),
          },
          workspaceMembership: {
            create: jest.fn().mockResolvedValue(mockMembership),
          },
        } as any);
      });

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation accepted successfully');
    });

    it('should handle transaction errors', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-1', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(null);
      (mockDb.role.findFirst as jest.Mock).mockResolvedValue(mockRole);

      // Mock transaction failure
      mockDb.$transaction.mockRejectedValue(new Error('Transaction failed'));

      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invite-1/accept');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to accept invitation');
    });
  });
});
