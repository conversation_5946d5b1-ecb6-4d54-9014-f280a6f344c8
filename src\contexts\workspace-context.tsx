'use client';

import { Workspace } from '@prisma/client';
import { deleteCookie, setCookie } from 'cookies-next';
import { createContext, ReactNode, useCallback, useContext, useEffect, useState } from 'react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { useApiServices } from '@/hooks/use-api-services';
import { logger } from '@/lib/logger/default-logger';
import { useTranslations } from 'next-intl';

// Constants for storage
const WORKSPACE_COOKIE_KEY = 'selected-workspace';

// TODO: currently all workspaces are fetched even outside workspace-selection page
// TODO: move the all workspace selection to the workspace-selection and here only fetch the selected one

interface WorkspaceContextType {
  // Current workspace state
  currentWorkspace: Workspace | null;
  workspaces: Workspace[];

  // Loading and error states
  loading: boolean;
  error: Error | null;

  // Workspace operations
  selectWorkspace: (_workspaceId: string) => Promise<void>;
  createWorkspace: (_data: { name: string; avatar?: string }) => Promise<Workspace>;
  deleteWorkspace: (_workspaceId: string) => Promise<void>;
  updateWorkspace: (_workspaceId: string, _data: { name?: string; avatar?: string }) => Promise<Workspace>;

  // Data management
  fetchWorkspaces: (_force?: boolean) => Promise<void>;
  fetchCurrentWorkspace: (_force?: boolean) => Promise<void>;
  clearWorkspaceData: () => void;

  // Utility methods
  clearError: () => void;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

interface WorkspaceProviderProps {
  children: ReactNode;
}

export function WorkspaceProvider({ children }: WorkspaceProviderProps) {
  const t = useTranslations('workspace');
  const { user } = useAuth();

  const { workspaceApiService } = useApiServices();

  // State management
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Track if initialization is in progress to prevent duplicate calls
  const [isInitializing, setIsInitializing] = useState(false);

  // Fetch current workspace from API
  const fetchCurrentWorkspace = useCallback(async (): Promise<void> => {
    // Don't fetch if user is not authenticated
    if (!user) {
      logger.debug('WorkspaceProvider: Skipping fetchCurrentWorkspace - user not authenticated');
      return;
    }

    try {
      setError(null);
      setLoading(true);

      const workspace = await workspaceApiService.getSelectedWorkspace();
      setCurrentWorkspace(workspace);

      // Update cookie to match the selected workspace
      if (workspace) {
        setCookie(WORKSPACE_COOKIE_KEY, workspace.id, {
          path: '/',
          maxAge: 30 * 24 * 60 * 60, // 30 days
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
        });
      } else {
        deleteCookie(WORKSPACE_COOKIE_KEY, { path: '/' });
      }
    } catch (err) {
      // If fetching current workspace fails (e.g., workspace was deleted),
      // clear the current workspace and cookie
      logger.warn('Failed to fetch current workspace, clearing selection:', err);
      setCurrentWorkspace(null);
      deleteCookie(WORKSPACE_COOKIE_KEY, { path: '/' });

      // Don't set this as an error since it's expected when workspace is deleted
      // The user will be shown the workspace selection page
    } finally {
      setLoading(false);
    }
  }, [workspaceApiService, user]);

  const fetchWorkspaces = useCallback(
    async (force = false): Promise<void> => {
      // Don't fetch if user is not authenticated
      if (!user) {
        logger.debug('WorkspaceProvider: Skipping fetchWorkspaces - user not authenticated');
        return;
      }

      try {
        setError(null);
        if (force) {
          setLoading(true);
        }

        const fetchedWorkspaces = await workspaceApiService.getWorkspaces();
        setWorkspaces(fetchedWorkspaces);
      } catch (err) {
        console.error('Error fetching workspaces:', err);
        setError(new Error(t('selection.errors.loadFailed')));
      } finally {
        if (force) {
          setLoading(false);
        }
      }
    },
    [workspaceApiService, t, user]
  );

  const selectWorkspace = useCallback(
    async (workspaceId: string): Promise<void> => {
      logger.debug('WorkspaceContext: selectWorkspace called', { workspaceId });

      try {
        setError(null);
        setLoading(true);
        logger.debug('WorkspaceContext: Loading set to true');

        const selectedWorkspace = await workspaceApiService.selectWorkspace(workspaceId);
        logger.debug('WorkspaceContext: API call successful', { selectedWorkspace: selectedWorkspace.id });
        setCurrentWorkspace(selectedWorkspace);

        // Update cookie
        setCookie(WORKSPACE_COOKIE_KEY, selectedWorkspace.id, {
          path: '/',
          maxAge: 30 * 24 * 60 * 60, // 30 days
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
        });
        logger.debug('WorkspaceContext: Cookie updated');
      } catch (err) {
        const selectError = err instanceof Error ? err : new Error('Failed to select workspace');
        setError(selectError);
        console.error('WorkspaceContext: Error selecting workspace:', selectError);
        throw selectError;
      } finally {
        setLoading(false);
        logger.debug('WorkspaceContext: Loading set to false');
      }
    },
    [workspaceApiService]
  );

  // Create a new workspace
  const createWorkspace = useCallback(
    async (data: { name: string; avatar?: string }): Promise<Workspace> => {
      try {
        setError(null);
        setLoading(true);

        const newWorkspace = await workspaceApiService.createWorkspace(data);
        logger.debug('WorkspaceProvider: Created new workspace:', newWorkspace);

        // Refresh the entire workspaces list to ensure consistency
        const updatedWorkspaces = await workspaceApiService.getWorkspaces();
        logger.debug(
          'WorkspaceProvider: Refreshed workspaces after creation:',
          updatedWorkspaces.length,
          updatedWorkspaces
        );
        setWorkspaces(updatedWorkspaces);

        return newWorkspace;
      } catch (err) {
        const createError = err instanceof Error ? err : new Error('Failed to create workspace');
        setError(createError);
        console.error('Error creating workspace:', createError);
        throw createError;
      } finally {
        setLoading(false);
      }
    },
    [workspaceApiService]
  );

  // Update a workspace
  const updateWorkspace = useCallback(
    async (workspaceId: string, data: { name?: string; avatar?: string }): Promise<Workspace> => {
      try {
        setError(null);
        setLoading(true);

        const updatedWorkspace = await workspaceApiService.updateWorkspace(workspaceId, data);

        // Update workspaces list
        setWorkspaces((prev) => prev.map((workspace) => (workspace.id === workspaceId ? updatedWorkspace : workspace)));

        // Update current workspace if it's the one being updated
        if (currentWorkspace?.id === workspaceId) {
          setCurrentWorkspace(updatedWorkspace);
        }

        return updatedWorkspace;
      } catch (err) {
        const updateError = err instanceof Error ? err : new Error('Failed to update workspace');
        setError(updateError);
        console.error('Error updating workspace:', updateError);
        throw updateError;
      } finally {
        setLoading(false);
      }
    },
    [workspaceApiService, currentWorkspace]
  );

  // Delete a workspace
  const deleteWorkspace = useCallback(
    async (workspaceId: string): Promise<void> => {
      try {
        setError(null);
        setLoading(true);

        await workspaceApiService.deleteWorkspace(workspaceId);

        // Remove from workspaces list
        setWorkspaces((prev) => prev.filter((workspace) => workspace.id !== workspaceId));

        // If the deleted workspace was the current one, clear it
        if (currentWorkspace?.id === workspaceId) {
          setCurrentWorkspace(null);
          deleteCookie(WORKSPACE_COOKIE_KEY, { path: '/' });
        }
      } catch (err) {
        const deleteError = err instanceof Error ? err : new Error('Failed to delete workspace');
        setError(deleteError);
        console.error('Error deleting workspace:', deleteError);
        throw deleteError;
      } finally {
        setLoading(false);
      }
    },
    [workspaceApiService, currentWorkspace]
  );

  // Clear workspace data
  const clearWorkspaceData = useCallback((): void => {
    setCurrentWorkspace(null);
    setWorkspaces([]);
    setError(null);
    deleteCookie(WORKSPACE_COOKIE_KEY, { path: '/' });
  }, []);

  // Clear error
  const clearError = useCallback((): void => {
    setError(null);
  }, []);

  // Listen to auth state changes and clear workspace data when user logs out
  useEffect(() => {
    // If user becomes null (logged out), clear all workspace data
    if (user === null) {
      logger.debug('WorkspaceProvider: User logged out, clearing workspace data');
      clearWorkspaceData();
      setLoading(false); // Stop loading state to prevent unnecessary API calls
      setIsInitializing(false); // Reset initialization state
    }
  }, [user, clearWorkspaceData]);

  // Initialize workspace data on mount
  useEffect(() => {
    // Skip if already initializing to prevent duplicate calls
    if (isInitializing) {
      return;
    }

    let mounted = true;

    // Add a small delay to debounce React Strict Mode double mounting
    const timeoutId = setTimeout(() => {
      if (!mounted) return;

      const initializeWorkspaces = async () => {
        try {
          setIsInitializing(true);
          setError(null);
          logger.debug('WorkspaceProvider: Starting initialization');

          // Only set loading to true if we don't have any workspace data yet
          if (!currentWorkspace && workspaces.length === 0) {
            setLoading(true);
            logger.debug('WorkspaceProvider: Set loading to true (no existing data)');
          } else {
            logger.debug('WorkspaceProvider: Skipping loading state (existing data found)', {
              currentWorkspace: currentWorkspace?.id,
              workspacesCount: workspaces.length,
            });
          }

          // Always fetch workspaces first
          const fetchedWorkspaces = await workspaceApiService.getWorkspaces();
          if (!mounted) return;

          setWorkspaces(fetchedWorkspaces);
          logger.debug('WorkspaceProvider: Fetched workspaces', { count: fetchedWorkspaces.length });

          // Try to fetch current workspace
          try {
            const workspace = await workspaceApiService.getSelectedWorkspace();
            if (!mounted) return;

            logger.debug('WorkspaceProvider: Fetched current workspace', { workspaceId: workspace?.id });
            setCurrentWorkspace(workspace);

            // Update cookie
            if (workspace) {
              setCookie(WORKSPACE_COOKIE_KEY, workspace.id, {
                path: '/',
                maxAge: 30 * 24 * 60 * 60, // 30 days
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
              });
            } else {
              deleteCookie(WORKSPACE_COOKIE_KEY, { path: '/' });
            }
          } catch (err) {
            // No current workspace selected - this is fine for workspace selection page
            if (!mounted) return;
            logger.warn('No current workspace selected:', err);
            setCurrentWorkspace(null);
            deleteCookie(WORKSPACE_COOKIE_KEY, { path: '/' });
          }
        } catch (initError) {
          if (!mounted) return;
          logger.error('Error fetching workspaces:', initError);

          setError(new Error(t('selection.errors.loadFailed')));
        } finally {
          if (mounted) {
            setLoading(false);
            setIsInitializing(false);
          }
        }
      };

      initializeWorkspaces();
    }, 100); // 100ms delay to debounce React Strict Mode

    return () => {
      mounted = false;
      clearTimeout(timeoutId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run when user changes

  const value: WorkspaceContextType = {
    currentWorkspace,
    workspaces,
    loading,
    error,
    selectWorkspace,
    createWorkspace,
    deleteWorkspace,
    updateWorkspace,
    fetchWorkspaces,
    fetchCurrentWorkspace,
    clearWorkspaceData,
    clearError,
  };

  return <WorkspaceContext.Provider value={value}>{children}</WorkspaceContext.Provider>;
}

// Hook to use the workspace context
export function useWorkspace(): WorkspaceContextType {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
}

// Export the context for advanced usage
export default WorkspaceContext;
export { WorkspaceContext };
