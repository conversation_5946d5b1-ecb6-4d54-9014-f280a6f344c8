import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';

import { InvitationHandler } from '@/components/invitations/invitation-handler';
import { isValidToken } from '@/lib/utils/token';

interface InvitePageProps {
  params: Promise<{
    locale: string;
    token: string;
  }>;
}

export async function generateMetadata({ params: _params }: InvitePageProps): Promise<Metadata> {
  const t = await getTranslations('workspace.invitations');

  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
  };
}

export default async function InvitePage({ params }: InvitePageProps) {
  const { token } = await params;

  // Validate token format before proceeding
  if (!isValidToken(token)) {
    notFound();
  }

  return <InvitationHandler token={token} />;
}
