/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { GET } from '../route';

// Mock dependencies
jest.mock('@/lib/auth/authentication', () => ({
  isUserAuthenticated: jest.fn(),
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceMembership: {
      findFirst: jest.fn(),
    },
    role: {
      findMany: jest.fn(),
    },
  },
}));

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { db } from '@/services/db';

const mockDb = db as jest.Mocked<typeof db>;
const mockIsUserAuthenticated = isUserAuthenticated as jest.MockedFunction<typeof isUserAuthenticated>;

describe('/api/workspace/[workspaceId]/roles', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: false, uid: '', email: '' });

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 403 when user is not a member of the workspace', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });

      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });

    it('should return 403 when user membership exists but userId is empty', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });

      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue({
        userId: '', // Empty userId should trigger forbidden response
      });

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
    });

    it('should return workspace roles when user is a member', async () => {
      const mockRoles = [
        { id: 'owner', name: 'Owner', workspaceId: null },
        { id: 'member', name: 'Member', workspaceId: null },
        { id: 'custom-role', name: 'Custom Role', workspaceId: 'test-workspace' },
      ];

      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });

      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue({
        userId: 'user-123',
      });

      (mockDb.role.findMany as jest.Mock).mockResolvedValue(mockRoles);

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockRoles);
      expect(mockDb.workspaceMembership.findFirst).toHaveBeenCalledWith({
        where: {
          userId: 'user-123',
          workspaceId: 'test-workspace',
        },
        select: {
          userId: true,
        },
      });
      expect(mockDb.role.findMany).toHaveBeenCalledWith({
        where: {
          OR: [{ workspaceId: 'test-workspace' }, { workspaceId: null }],
        },
        select: {
          id: true,
          name: true,
          workspaceId: true,
        },
        orderBy: [{ workspaceId: 'asc' }, { name: 'asc' }],
      });
    });

    it('should handle database errors gracefully', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });

      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue({
        userId: 'user-123',
      });

      (mockDb.role.findMany as jest.Mock).mockRejectedValue(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to get workspace roles');
    });
  });
});
