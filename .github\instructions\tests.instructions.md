---
applyTo: '**/*.test.tsx,**/*.test.ts'
---

## Tests

- When implementing tests, there is no need to mock translation functions. It is already mocked on jest.setup.js. The mock will always return the key of the translation as the translation value.
- Do not mock react and pure frontend components, they should be tested together with the rest of the tests.
- To run tests and check its output, always use `npm run test:coverage` and pass the file to be tested. DO NOT USE the native test execution tool.
- Coverage files for each file are saved in the coverage folder #file:../../TestResults/coverage/clover.xml
- Check how we setup jest on #file:../../jest.setup.js and do not mock unecessary things that are already configured in there.
- When mocking api services on tests, use mock definition on #file:../../src/hooks/__mocks__/use-api-services.ts
- For tests ignore the translation keys. Assume that the key will be the text shown after the component is rendered.
- DO NOT USE `testPathPattern`, jest now uses the parameter `testPathPatterns` when executing tests! Or you should pass the file path as the example: `npm run test:coverage <filePath>`.
  - If running on powershell instead of bash, remember to use linux path separator when specifying the test file path and make sure to escape any characters that need to be escaped (e.g. `[` and `]`).