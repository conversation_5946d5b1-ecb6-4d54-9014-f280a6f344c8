import { Role, User, Workspace, WorkspaceInvite } from '@prisma/client';

import { CoreApiService } from './core';

/**
 * User API service
 * This service provides methods for interacting with the user API
 */
export class UserApiService {
  private api: CoreApiService;

  /**
   * Create a new UserApiService
   * @param apiService The API service to use
   */
  constructor(apiService: CoreApiService) {
    this.api = apiService;
  }

  /**
   * Get the current user or create a new one if they don't exist
   * @returns A promise that resolves to the user data
   */
  async getCurrentUser(): Promise<User> {
    return await this.api.request({
      method: 'GET',
      url: '/user',
    });
  }

  /**
   * Update the current user's profile information
   * @param user The user data to update, only the provided fields will be updated
   * @returns A promise that resolves to the updated user data
   */
  async updateCurrentUser(user: Partial<User>): Promise<User> {
    return await this.api.request({
      method: 'PATCH',
      url: '/user',
      data: user,
    });
  }

  async createAvatarPresignedUrl(
    contentType: string,
    contentLength: number
  ): Promise<{ url: string; expiration: string; path: string; cacheControl: string }> {
    return await this.api.request({
      method: 'POST',
      url: `/user/avatar?content-type=${contentType}&content-length=${contentLength}`,
    });
  }

  /**
   * Complete the onboarding process
   * @param onboardingData The onboarding data
   * @returns A promise that resolves to the updated user data
   */
  async completeOnboarding(onboardingData: {
    displayName: string;
    country?: string;
    timezone?: string;
    phone?: string;
    language?: string;
    theme?: 'light' | 'dark' | 'system';
  }): Promise<User> {
    return await this.api.request({
      method: 'PATCH',
      url: '/user/onboarding',
      data: onboardingData,
    });
  }

  /**
   * Get user's pending invitations
   * @returns A promise that resolves to an array of pending invitations for the current user
   */
  async getUserInvitations() {
    const result = await this.api.request<
      (WorkspaceInvite & { workspace: Workspace; invitedByUser: User; role: Role | null })[]
    >({
      method: 'GET',
      url: '/user/invites',
    });

    if (result) {
      return result.map((invitation) => ({
        ...invitation,
        createdAt: new Date(invitation.createdAt),
        updatedAt: new Date(invitation.updatedAt),
      }));
    }

    return result;
  }
}

/**
 * Create a user API service with the given API service
 * @param apiService The API service to use
 * @returns A new user API service
 */
export function createUserApiService(apiService: CoreApiService): UserApiService {
  return new UserApiService(apiService);
}
