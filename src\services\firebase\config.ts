// This file provides Firebase configuration for both development and production environments

import { isRunningOnAppHosting } from '@/lib/server/host';

// Function to parse the FIREBASE_WEBAPP_CONFIG environment variable
function getFirebaseConfig() {
  // Check if we're in a Firebase App Hosting environment
  // Firebase App Hosting sets the firebase environment variables automatically
  if (isRunningOnAppHosting()) {
    return undefined;
  }

  // Fallback to environment variables
  // Useful for local development
  return {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
    databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
  };
}

export const firebaseConfig = getFirebaseConfig();
