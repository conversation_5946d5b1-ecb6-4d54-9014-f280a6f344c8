'use client';

import {
  <PERSON>complete,
  Button,
  CircularProgress,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Stack,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { CompanySize } from '@prisma/client';
import { useTranslations } from 'next-intl';
import React from 'react';

interface LeadCaptureModalProps {
  open: boolean;
  onClose: () => void;
}

interface FormData {
  fullName: string;
  email: string;
  companyName: string;
  jobTitle: string;
  companySize: CompanySize | null;
  hearAboutUs: string;
}

interface FormErrors {
  fullName?: string;
  email?: string;
  companyName?: string;
  jobTitle?: string;
  companySize?: string;
  hearAboutUs?: string;
  general?: string;
}

const companySizeOptions = [
  { value: CompanySize.STARTUP_1_10, labelKey: 'startup' },
  { value: CompanySize.SMALL_11_50, labelKey: 'small' },
  { value: CompanySize.MEDIUM_51_200, labelKey: 'medium' },
  { value: CompanySize.LARGE_201_1000, labelKey: 'large' },
  { value: CompanySize.ENTERPRISE_1000_PLUS, labelKey: 'enterprise' },
];

const hearAboutUsOptions = [
  { value: 'search', labelKey: 'search' },
  { value: 'social_media', labelKey: 'socialMedia' },
  { value: 'referral', labelKey: 'referral' },
  { value: 'conference', labelKey: 'conference' },
  { value: 'blog', labelKey: 'blog' },
  { value: 'other', labelKey: 'other' },
];

export function LeadCaptureModal({ open, onClose }: LeadCaptureModalProps): React.JSX.Element {
  const theme = useTheme();
  const t = useTranslations('landing.leadCapture');

  const [formData, setFormData] = React.useState<FormData>({
    fullName: '',
    email: '',
    companyName: '',
    jobTitle: '',
    companySize: null,
    hearAboutUs: '',
  });

  const [errors, setErrors] = React.useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);

  const handleInputChange = (field: keyof FormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({ ...prev, [field]: event.target.value }));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = t('errors.fullNameRequired');
    } else if (formData.fullName.length > 100) {
      newErrors.fullName = t('errors.fullNameTooLong');
    }

    if (!formData.email.trim()) {
      newErrors.email = t('errors.emailRequired');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t('errors.emailInvalid');
    }

    if (!formData.companyName.trim()) {
      newErrors.companyName = t('errors.companyNameRequired');
    } else if (formData.companyName.length > 100) {
      newErrors.companyName = t('errors.companyNameTooLong');
    }

    if (!formData.companySize) {
      newErrors.companySize = t('errors.companySizeRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fullName: formData.fullName.trim(),
          email: formData.email.trim(),
          companyName: formData.companyName.trim(),
          jobTitle: formData.jobTitle.trim() || undefined,
          companySize: formData.companySize,
          hearAboutUs: formData.hearAboutUs.trim() || undefined,
        }),
      });

      if (response.ok) {
        setIsSuccess(true);
      } else {
        const errorData = await response.json();
        if (response.status === 409) {
          setErrors({ email: t('errors.emailExists') });
        } else {
          setErrors({ general: errorData.error || t('errors.submitFailed') });
        }
      }
    } catch (error) {
      console.error('Error submitting lead:', error);
      setErrors({ general: t('errors.networkError') });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        fullName: '',
        email: '',
        companyName: '',
        jobTitle: '',
        companySize: null,
        hearAboutUs: '',
      });
      setErrors({});
      setIsSuccess(false);
      onClose();
    }
  };

  if (isSuccess) {
    return (
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth='sm'
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: theme.palette.background.paper,
            backgroundImage: 'none',
          },
        }}
      >
        <DialogContent sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant='h5' gutterBottom color='primary'>
            {t('success.title')}
          </Typography>
          <Typography variant='body1' color='text.secondary' sx={{ mb: 3 }}>
            {t('success.message')}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
          <Button variant='contained' onClick={handleClose} size='large'>
            {t('success.close')}
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth='sm' fullWidth>
      <DialogTitle>
        <Typography variant='h5' component='div'>
          {t('title')}
        </Typography>
        <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
          {t('subtitle')}
        </Typography>
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Stack spacing={3}>
            {errors.general && (
              <Typography color='error' variant='body2'>
                {errors.general}
              </Typography>
            )}

            <TextField
              fullWidth
              label={t('fields.fullName')}
              value={formData.fullName}
              onChange={handleInputChange('fullName')}
              error={!!errors.fullName}
              helperText={errors.fullName}
              required
              disabled={isSubmitting}
            />

            <TextField
              fullWidth
              label={t('fields.email')}
              type='email'
              value={formData.email}
              onChange={handleInputChange('email')}
              error={!!errors.email}
              helperText={errors.email}
              required
              disabled={isSubmitting}
            />

            <TextField
              fullWidth
              label={t('fields.companyName')}
              value={formData.companyName}
              onChange={handleInputChange('companyName')}
              error={!!errors.companyName}
              helperText={errors.companyName}
              required
              disabled={isSubmitting}
            />

            <TextField
              fullWidth
              label={t('fields.jobTitle')}
              value={formData.jobTitle}
              onChange={handleInputChange('jobTitle')}
              disabled={isSubmitting}
            />

            <FormControl fullWidth error={!!errors.companySize}>
              <Autocomplete
                options={companySizeOptions}
                getOptionLabel={(option) => {
                  switch (option.labelKey) {
                    case 'startup':
                      return t('companySize.startup');
                    case 'small':
                      return t('companySize.small');
                    case 'medium':
                      return t('companySize.medium');
                    case 'large':
                      return t('companySize.large');
                    case 'enterprise':
                      return t('companySize.enterprise');
                    default:
                      return option.labelKey;
                  }
                }}
                value={companySizeOptions.find((option) => option.value === formData.companySize) || null}
                onChange={(_, newValue) => {
                  setFormData((prev) => ({ ...prev, companySize: newValue?.value || null }));
                  if (errors.companySize) {
                    setErrors((prev) => ({ ...prev, companySize: undefined }));
                  }
                }}
                disabled={isSubmitting}
                renderInput={(params) => (
                  <TextField {...params} label={t('fields.companySize')} required error={!!errors.companySize} />
                )}
              />
              {errors.companySize && <FormHelperText>{errors.companySize}</FormHelperText>}
            </FormControl>

            <Autocomplete
              options={hearAboutUsOptions}
              getOptionLabel={(option) => {
                switch (option.labelKey) {
                  case 'search':
                    return t('hearAboutUs.search');
                  case 'socialMedia':
                    return t('hearAboutUs.socialMedia');
                  case 'referral':
                    return t('hearAboutUs.referral');
                  case 'conference':
                    return t('hearAboutUs.conference');
                  case 'blog':
                    return t('hearAboutUs.blog');
                  case 'other':
                    return t('hearAboutUs.other');
                  default:
                    return option.labelKey;
                }
              }}
              value={hearAboutUsOptions.find((option) => option.value === formData.hearAboutUs) || null}
              onChange={(_, newValue) => {
                setFormData((prev) => ({ ...prev, hearAboutUs: newValue?.value || '' }));
              }}
              disabled={isSubmitting}
              renderInput={(params) => <TextField {...params} label={t('fields.hearAboutUs')} />}
            />
          </Stack>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={handleClose} disabled={isSubmitting}>
            {t('actions.cancel')}
          </Button>
          <Button
            type='submit'
            variant='contained'
            disabled={isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={20} /> : undefined}
          >
            {isSubmitting ? t('actions.submitting') : t('actions.submit')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}
