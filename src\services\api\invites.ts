import { WorkspaceInvite } from '@prisma/client';

import { CoreApiService } from './core';

/**
 * Invites API service
 * This service provides methods for interacting with the invites API
 */
export class InvitesApiService {
  private api: CoreApiService;

  /**
   * Create a new InvitesApiService
   * @param apiService The API service to use
   */
  constructor(apiService: CoreApiService) {
    this.api = apiService;
  }

  /**
   * Get an invite by token
   * @param token The invite token
   * @returns A promise that resolves to the invite
   */
  async getInvite(token: string) {
    const workspaceInvite = await this.api.request<
      | (WorkspaceInvite & {
          workspace: {
            id: string;
            name: string;
          };
          role: {
            id: string;
            name: string;
          };
          invitedByUser: {
            id: string;
            email: string;
            displayName: string;
          };
        })
      | undefined
    >({
      method: 'GET',
      url: `/invites/token/${token}`,
    });

    if (workspaceInvite) {
      workspaceInvite.createdAt = new Date(workspaceInvite.createdAt);
      workspaceInvite.updatedAt = new Date(workspaceInvite.updatedAt);
    }

    return workspaceInvite;
  }

  /**
   * Accept an invite by token
   * @param token The invite token
   * @returns A promise that resolves to the acceptance response
   */
  async acceptInviteByToken(token: string) {
    const response = await this.api.request<{
      message: string;
      workspace: {
        id: string;
        name: string;
      };
      membership: {
        id: string;
        role: {
          id: string;
          name: string;
        };
      };
    }>({
      method: 'POST',
      url: `/invites/token/${token}/accept`,
    });

    return response;
  }

  /**
   * Decline an invite by token
   * @param token The invite token
   * @returns A promise that resolves to the decline response
   */
  async declineInviteByToken(token: string) {
    const response = await this.api.request<{
      message: string;
      workspace: {
        id: string;
        name: string;
      };
    }>({
      method: 'POST',
      url: `/invites/token/${token}/decline`,
    });

    return response;
  }
}

/**
 * Create a invites API service with the given API service
 * @param apiService The API service to use
 * @returns A new invites API service
 */
export function createInvitesApiService(apiService: CoreApiService): InvitesApiService {
  return new InvitesApiService(apiService);
}
