/**
 * @jest-environment node
 */

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { NextRequest } from 'next/server';
import { POST } from '../route';

// Mock the dependencies
jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findFirst: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

jest.mock('@/lib/auth/authentication', () => ({
  isUserAuthenticated: jest.fn(),
}));

// Import mocked modules
const mockDb = db as jest.Mocked<typeof db>;
const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;
const mockIsUserAuthenticated = isUserAuthenticated as jest.MockedFunction<typeof isUserAuthenticated>;

// Type cast the specific methods to jest mocks
const mockWorkspaceInviteFindFirst = mockDb.workspaceInvite.findFirst as jest.MockedFunction<
  typeof mockDb.workspaceInvite.findFirst
>;
const mockWorkspaceInviteUpdate = mockDb.workspaceInvite.update as jest.MockedFunction<
  typeof mockDb.workspaceInvite.update
>;

describe('/api/invites/token/[token]/decline - POST', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default to authenticated user
    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    // Default Firebase mock to avoid errors
    mockGetAuthenticatedAppForUser.mockResolvedValue({
      firebaseServerApp: {} as any,
      currentUser: {
        uid: 'user-1',
        email: '<EMAIL>',
      } as any,
    } as any);
  });

  const mockInvitation = {
    id: 'invite-1',
    email: '<EMAIL>',
    status: 'PENDING',
    workspaceId: 'workspace-1',
    roleId: 'role-1',
    workspace: {
      id: 'workspace-1',
      name: 'Test Workspace',
    },
    role: {
      id: 'role-1',
      name: 'Member',
    },
  };

  it('should decline invitation successfully', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockWorkspaceInviteFindFirst.mockResolvedValue(mockInvitation as any);
    mockWorkspaceInviteUpdate.mockResolvedValue({ ...mockInvitation, status: 'REJECTED' } as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.message).toBe('Invitation declined successfully');
    expect(data.workspace).toEqual(mockInvitation.workspace);
    expect(mockWorkspaceInviteUpdate).toHaveBeenCalledWith({
      where: { id: mockInvitation.id },
      data: {
        status: 'REJECTED',
        updatedAt: expect.any(Date),
      },
    });
  });

  it('should return 400 for invalid token format', async () => {
    const invalidToken = 'invalid-token';

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: invalidToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Invalid token format');
    expect(mockIsUserAuthenticated).not.toHaveBeenCalled();
  });

  it('should return 401 for authentication system failures - no firebaseServerApp', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockIsUserAuthenticated.mockResolvedValue({
      ok: false,
      uid: null,
      email: null,
    } as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe('Unauthorized');
  });

  it('should return 401 for authentication system failures - no currentUser', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockIsUserAuthenticated.mockResolvedValue({
      ok: false,
      uid: null,
      email: null,
    } as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe('Unauthorized');
  });

  it('should return 404 for non-existent invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockWorkspaceInviteFindFirst.mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(404);
    expect(data.error).toBe('Invitation not found or expired');
  });

  it('should return 410 for already accepted invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const acceptedInvitation = { ...mockInvitation, status: 'ACCEPTED' };

    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockWorkspaceInviteFindFirst.mockResolvedValue(acceptedInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(410);
    expect(data.error).toBe('Invitation already processed');
    expect(data.status).toBe('ACCEPTED');
  });

  it('should return 410 for already rejected invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const rejectedInvitation = { ...mockInvitation, status: 'REJECTED' };

    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockWorkspaceInviteFindFirst.mockResolvedValue(rejectedInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(410);
    expect(data.error).toBe('Invitation already processed');
    expect(data.status).toBe('REJECTED');
  });

  it('should return 403 for email mismatch', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const mismatchInvitation = { ...mockInvitation, email: '<EMAIL>' };

    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockWorkspaceInviteFindFirst.mockResolvedValue(mismatchInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(403);
    expect(data.error).toBe('Email mismatch');
    expect(data.message).toBe('This invitation is for a different email address');
  });

  it('should handle database errors during invitation lookup', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockWorkspaceInviteFindFirst.mockRejectedValue(new Error('Database error'));

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to decline invitation');
  });

  it('should handle database errors during invitation update', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockWorkspaceInviteFindFirst.mockResolvedValue(mockInvitation as any);
    mockWorkspaceInviteUpdate.mockRejectedValue(new Error('Database update error'));

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to decline invitation');
  });

  it('should handle authentication service errors', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockIsUserAuthenticated.mockRejectedValue(new Error('Authentication service error'));

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/decline', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to decline invitation');
  });
});
