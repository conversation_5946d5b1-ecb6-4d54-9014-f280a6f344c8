import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { logger } from '../logger/default-logger';

/**
 * Check if a user is authenticated.
 *
 * This function should be used on route.ts files to check if the user is authenticated.
 *
 * @returns True if the user is authenticated, false otherwise
 */
export async function isUserAuthenticated(): Promise<{ ok: boolean; uid: string; email: string }> {
  try {
    const authResult = await getAuthenticatedAppForUser();

    return {
      ok: !!authResult?.currentUser,
      uid: authResult?.currentUser?.uid || '',
      email: authResult?.currentUser?.email || '',
    };
  } catch (error) {
    logger.error('Error checking for user authentication:', error);

    return {
      ok: false,
      uid: '',
      email: '',
    };
  }
}
