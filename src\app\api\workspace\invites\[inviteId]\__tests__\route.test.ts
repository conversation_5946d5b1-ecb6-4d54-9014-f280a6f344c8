/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/lib/auth/authentication', () => ({
  isUserAuthenticated: jest.fn(),
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
    workspaceMembership: {
      findFirst: jest.fn(),
    },
  },
}));

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { db } from '@/services/db';

import { DELETE } from '../route';

const mockDb = db as jest.Mocked<typeof db>;
const mockIsUserAuthenticated = isUserAuthenticated as jest.MockedFunction<typeof isUserAuthenticated>;

// Mock data
const validInviteId = 'a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2'; // 64 char hex
const mockInvitation = {
  id: validInviteId,
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'user-1',
  userId: null,
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
};

const mockWorkspaceMembership = {
  id: 'membership-1',
  userId: 'user-1',
  workspaceId: 'workspace-1',
  roleId: 'admin',
};

describe('/api/workspace/invites/[inviteId]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('DELETE', () => {
    it('should return 400 for invalid token', async () => {
      const request = new NextRequest('http://localhost:3000/api/workspace/invites/invalid');
      const params = Promise.resolve({ inviteId: 'invalid' });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid token');
    });

    it('should return 401 when user is not authenticated', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: false, uid: '', email: '' });

      const request = new NextRequest(`http://localhost:3000/api/workspace/invites/${validInviteId}`);
      const params = Promise.resolve({ inviteId: validInviteId });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 400 when invitation is not found', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest(`http://localhost:3000/api/workspace/invites/${validInviteId}`);
      const params = Promise.resolve({ inviteId: validInviteId });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invitation not found');
    });

    it('should return 400 when invitation is not pending', async () => {
      const acceptedInvitation = { ...mockInvitation, status: 'ACCEPTED' };
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(acceptedInvitation);

      const request = new NextRequest(`http://localhost:3000/api/workspace/invites/${validInviteId}`);
      const params = Promise.resolve({ inviteId: validInviteId });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Only pending invitations can be cancelled');
    });

    it('should return 403 when user does not have MANAGE_INVITATIONS permission', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(null);

      const request = new NextRequest(`http://localhost:3000/api/workspace/invites/${validInviteId}`);
      const params = Promise.resolve({ inviteId: validInviteId });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
      expect(data.missingPermissions).toEqual(['MANAGE_INVITATIONS']);
    });

    it('should delete invitation successfully when user has MANAGE_INVITATIONS permission', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(mockWorkspaceMembership);
      (mockDb.workspaceInvite.delete as jest.Mock).mockResolvedValue(mockInvitation);

      const request = new NextRequest(`http://localhost:3000/api/workspace/invites/${validInviteId}`);
      const params = Promise.resolve({ inviteId: validInviteId });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation cancelled successfully');
      expect(mockDb.workspaceInvite.delete).toHaveBeenCalledWith({
        where: { id: validInviteId },
      });
      expect(mockDb.workspaceMembership.findFirst).toHaveBeenCalledWith({
        where: {
          userId: 'user-123',
          workspaceId: 'workspace-1',
          role: {
            permissions: {
              some: {
                id: 'MANAGE_INVITATIONS',
              },
            },
          },
        },
      });
    });

    it('should handle database errors gracefully during permission check', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockRejectedValue(new Error('Database error'));

      const request = new NextRequest(`http://localhost:3000/api/workspace/invites/${validInviteId}`);
      const params = Promise.resolve({ inviteId: validInviteId });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
      expect(data.missingPermissions).toEqual(['MANAGE_INVITATIONS']);
    });

    it('should handle database errors during deletion', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: true, uid: 'user-123', email: '<EMAIL>' });
      (mockDb.workspaceInvite.findUnique as jest.Mock).mockResolvedValue(mockInvitation);
      (mockDb.workspaceMembership.findFirst as jest.Mock).mockResolvedValue(mockWorkspaceMembership);
      (mockDb.workspaceInvite.delete as jest.Mock).mockRejectedValue(new Error('Database error'));

      const request = new NextRequest(`http://localhost:3000/api/workspace/invites/${validInviteId}`);
      const params = Promise.resolve({ inviteId: validInviteId });

      const response = await DELETE(request, { params });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to cancel invitation');
    });
  });
});
