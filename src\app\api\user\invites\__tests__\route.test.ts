/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { GET } from '../route';

// Mock dependencies
jest.mock('@/services/firebase/admin', () => ({
  adminAuth: {
    verifyIdToken: jest.fn(),
  },
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findMany: jest.fn(),
    },
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

// Import mocked modules
import { db } from '@/services/db';
import { adminAuth } from '@/services/firebase/admin';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

const _mockAdminAuth = adminAuth as jest.Mocked<typeof adminAuth>;
const mockDb = db as jest.Mocked<typeof db>;
const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;

// Helper function to create mock request
function createMockRequest(method: string, token?: string): NextRequest {
  const url = 'http://localhost:3000/api/user/invites';
  const headers = new Headers();
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  headers.set('Content-Type', 'application/json');

  return new NextRequest(url, {
    method,
    headers,
  });
}

// Mock data
const mockUser = {
  uid: 'user-1',
  email: '<EMAIL>',
  displayName: 'Test User',
};

const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'inviter-1',
  userId: 'user-1',
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
  workspace: {
    id: 'workspace-1',
    name: 'Test Workspace',
    avatar: null,
  },
  invitedByUser: {
    id: 'inviter-1',
    email: '<EMAIL>',
    displayName: 'Inviter User',
  },
  role: {
    id: 'member',
    name: 'Member',
  },
};

describe('/api/user/invites', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return user invitations successfully', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findMany.mockResolvedValue([mockInvitation] as any);

      const request = createMockRequest('GET', 'valid-token');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveLength(1);
      expect(data[0].id).toBe('invite-1');
      expect(data[0].email).toBe('<EMAIL>');
      expect(data[0].workspace.name).toBe('Test Workspace');
      expect(mockDb.workspaceInvite.findMany).toHaveBeenCalledWith({
        where: {
          status: 'PENDING',
          OR: [{ userId: 'user-1' }, { email: '<EMAIL>' }],
        },
        include: {
          workspace: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          invitedByUser: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          role: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should return empty array when no invitations exist', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findMany.mockResolvedValue([]);

      const request = createMockRequest('GET', 'valid-token');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual([]);
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any);

      const request = createMockRequest('GET');
      const response = await GET(request);

      expect(response.status).toBe(401);
    });

    it('should handle database errors', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findMany.mockRejectedValue(new Error('Database error'));

      const request = createMockRequest('GET', 'valid-token');
      const response = await GET(request);

      expect(response.status).toBe(500);
    });

    it('should filter by both userId and email', async () => {
      const userWithoutEmail = { uid: 'user-1', email: null, displayName: 'Test User' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: userWithoutEmail,
      } as any);
      mockDb.workspaceInvite.findMany.mockResolvedValue([]);

      const request = createMockRequest('GET', 'valid-token');
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(mockDb.workspaceInvite.findMany).toHaveBeenCalledWith({
        where: {
          status: 'PENDING',
          OR: [{ userId: 'user-1' }, { email: '' }],
        },
        include: {
          workspace: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          invitedByUser: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          role: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should return multiple invitations ordered by creation date', async () => {
      const invitation2 = {
        ...mockInvitation,
        id: 'invite-2',
        workspaceId: 'workspace-2',
        workspace: {
          id: 'workspace-2',
          name: 'Another Workspace',
          avatar: null,
        },
        createdAt: new Date(Date.now() + 1000), // Newer invitation
      };

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findMany.mockResolvedValue([invitation2, mockInvitation] as any);

      const request = createMockRequest('GET', 'valid-token');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toHaveLength(2);
      expect(data[0].id).toBe('invite-2'); // Newer invitation first
      expect(data[1].id).toBe('invite-1');
    });
  });
});
