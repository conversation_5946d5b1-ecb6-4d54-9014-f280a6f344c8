import Stack from '@mui/material/Stack';
import type { Metadata } from 'next';
import * as React from 'react';

import { AccountDetailsForm } from '@/components/settings/account/account-details-form';
import { config } from '@/config';

export const metadata = {
  title: `Account | Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  return (
    <Stack spacing={3}>
      <AccountDetailsForm />
    </Stack>
  );
}
