'use client';

import { AppRouterCacheProvider } from '@mui/material-nextjs/v13-appRouter';
import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import * as React from 'react';

import { createTheme } from '@/styles/theme/create-theme';

import { Theme } from '@/styles/theme/types';
import EmotionCache from './emotion-cache';

export interface ThemeProviderProps {
  children: React.ReactNode;
  options?: Partial<Theme>;
}

export function ThemeProvider({ children, options }: ThemeProviderProps): React.JSX.Element {
  const theme = createTheme(options);

  return (
    <EmotionCache options={{ key: 'mui' }}>
      <AppRouterCacheProvider>
        <MuiThemeProvider theme={theme} defaultMode='system'>
          <CssBaseline />
          {children}
        </MuiThemeProvider>
      </AppRouterCacheProvider>
    </EmotionCache>
  );
}
