import 'server-only';

import { InviteStatus, Permission } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { z as zod } from 'zod';

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { checkUserPermission } from '@/lib/auth/authorization';
import { forbiddenResponse, unauthorizedResponse } from '@/lib/auth/responses';
import { generateUrlSafeToken, hashToken } from '@/lib/utils/token';
import { db } from '@/services/db';

// Schema for invitation creation
const createInviteSchema = zod.object({
  email: zod.string().email('Invalid email address'),
  roleId: zod.string().min(1, 'Role ID is required'),
});

/**
 * @swagger
 * /api/workspace/{workspaceId}/invites:
 *   post:
 *     summary: Create workspace invitation
 *     description: Create a new invitation to join a workspace. Only users with MANAGE_INVITATIONS permission can create invitations.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the user to invite
 *               roleId:
 *                 type: string
 *                 description: Role ID to assign to the invited user
 *             required:
 *               - email
 *               - roleId
 *     responses:
 *       201:
 *         description: Invitation created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/WorkspaceInvite'
 *                 - type: object
 *                   properties:
 *                     invitedByUser:
 *                       type: object
 *                       properties:
 *                         id:
 *                           allOf:
 *                             - $ref: '#/components/schemas/User/properties/id'
 *                         email:
 *                           allOf:
 *                             - $ref: '#/components/schemas/User/properties/email'
 *                         displayName:
 *                           allOf:
 *                             - $ref: '#/components/schemas/User/properties/displayName'
 *                     user:
 *                       type: object
 *                       nullable: true
 *                       properties:
 *                         id:
 *                           allOf:
 *                             - $ref: '#/components/schemas/User/properties/id'
 *                         email:
 *                           allOf:
 *                             - $ref: '#/components/schemas/User/properties/email'
 *                         displayName:
 *                           allOf:
 *                             - $ref: '#/components/schemas/User/properties/displayName'
 *                     role:
 *                       type: object
 *                       properties:
 *                         id:
 *                           allOf:
 *                             - $ref: '#/components/schemas/Role/properties/id'
 *                         name:
 *                           allOf:
 *                             - $ref: '#/components/schemas/Role/properties/name'
 *       400:
 *         description: Bad request - invalid input or user already invited/member
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have MANAGE_INVITATIONS permission
 *       500:
 *         description: Internal server error
 */
export async function POST(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    const { ok, uid } = await isUserAuthenticated();

    if (!ok) {
      return unauthorizedResponse();
    }

    if (!(await checkUserPermission(uid, workspaceId, Permission.MANAGE_INVITATIONS))) {
      return forbiddenResponse([Permission.MANAGE_INVITATIONS]);
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = createInviteSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: 'Invalid request', details: validationResult.error.format() }, { status: 400 });
    }

    const { email, roleId } = validationResult.data;

    // Check if the role exists and belongs to this workspace or is a global role
    const role = await db.role.findFirst({
      where: {
        id: roleId,
        OR: [
          { workspaceId: workspaceId },
          { workspaceId: null }, // Global roles
        ],
      },
    });

    if (!role) {
      return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 });
    }

    // Check if user is already a member of the workspace
    const existingMembership = await db.workspaceMembership.findFirst({
      where: {
        workspaceId: workspaceId,
        user: {
          email: email,
        },
      },
      select: {
        userId: true,
      },
    });

    if (existingMembership && existingMembership.userId) {
      return NextResponse.json(
        {
          error: 'User is already a member of this workspace',
          userAlreadyOnWorkspace: true,
          userAlreadyInvited: false,
        },
        { status: 400 }
      );
    }

    // Check if there's already a pending invitation for this email
    const existingInvite = await db.workspaceInvite.findFirst({
      where: {
        workspaceId: workspaceId,
        email: email,
        status: InviteStatus.PENDING,
      },
    });

    if (existingInvite) {
      return NextResponse.json(
        {
          error: 'User already has a pending invitation to this workspace',
          userAlreadyOnWorkspace: false,
          userAlreadyInvited: true,
        },
        { status: 400 }
      );
    }

    // Check if the user exists in the system
    const existingUser = await db.user.findFirst({
      where: {
        email: email,
      },
      select: {
        id: true,
      },
    });

    // Generate a secure token for the invitation link
    const invitationToken = generateUrlSafeToken();

    // Create the invitation
    const invitation = await db.workspaceInvite.create({
      data: {
        email: email,
        workspaceId: workspaceId,
        invitedBy: uid,
        userId: existingUser?.id || null,
        roleId: roleId,
        status: InviteStatus.PENDING,
        token: hashToken(invitationToken),
      },
      include: {
        invitedByUser: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // TODO: Send invitation email with the token
    // await sendInvitationEmail(email, invitationToken, workspace, inviterUser);

    return NextResponse.json(invitation, { status: 201 });
  } catch (error) {
    console.error('Error creating workspace invitation:', error);
    return NextResponse.json({ error: 'Failed to create invitation' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/workspace/{workspaceId}/invites:
 *   get:
 *     summary: Get workspace invitations
 *     description: Retrieve all invitations for a specific workspace. Only users with MANAGE_INVITATIONS permission can access this endpoint.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     responses:
 *       200:
 *         description: Successfully retrieved workspace invitations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 allOf:
 *                   - $ref: '#/components/schemas/WorkspaceInvite'
 *                   - type: object
 *                     properties:
 *                       invitedByUser:
 *                         type: object
 *                         properties:
 *                           id:
 *                             allOf:
 *                               - $ref: '#/components/schemas/User/properties/id'
 *                           email:
 *                             allOf:
 *                               - $ref: '#/components/schemas/User/properties/email'
 *                           displayName:
 *                             allOf:
 *                               - $ref: '#/components/schemas/User/properties/displayName'
 *                       user:
 *                         type: object
 *                         nullable: true
 *                         properties:
 *                           id:
 *                             allOf:
 *                               - $ref: '#/components/schemas/User/properties/id'
 *                           email:
 *                             allOf:
 *                               - $ref: '#/components/schemas/User/properties/email'
 *                           displayName:
 *                             allOf:
 *                               - $ref: '#/components/schemas/User/properties/displayName'
 *                       role:
 *                         type: object
 *                         properties:
 *                           id:
 *                             allOf:
 *                               - $ref: '#/components/schemas/Role/properties/id'
 *                           name:
 *                             allOf:
 *                               - $ref: '#/components/schemas/Role/properties/name'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user does not have MANAGE_INVITATIONS permission
 *       500:
 *         description: Internal server error
 */
export async function GET(_request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    const { ok, uid } = await isUserAuthenticated();

    if (!ok) {
      return unauthorizedResponse();
    }

    if (!(await checkUserPermission(uid, workspaceId, Permission.MANAGE_INVITATIONS))) {
      return forbiddenResponse([Permission.MANAGE_INVITATIONS]);
    }

    // Get all invitations for the workspace
    const invitations = await db.workspaceInvite.findMany({
      where: {
        workspaceId: workspaceId,
      },
      include: {
        invitedByUser: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(invitations);
  } catch (error) {
    console.error('Error getting workspace invitations:', error);
    return NextResponse.json({ error: 'Failed to get invitations' }, { status: 500 });
  }
}
