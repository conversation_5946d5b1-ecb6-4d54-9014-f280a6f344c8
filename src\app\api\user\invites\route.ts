import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { unauthorizedResponse } from '@/lib/auth/responses';
import { db } from '@/services/db';
import { InviteStatus } from '@prisma/client';

/**
 * @swagger
 * /api/user/invites:
 *   get:
 *     summary: Get user's pending invitations
 *     description: Retrieve all pending workspace invitations for the current user.
 *     tags:
 *       - User
 *     responses:
 *       200:
 *         description: Successfully retrieved user's pending invitations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   email:
 *                     type: string
 *                   status:
 *                     type: string
 *                     enum: [PENDING]
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                   workspaceId:
 *                     type: string
 *                   invitedBy:
 *                     type: string
 *                   userId:
 *                     type: string
 *                     nullable: true
 *                   workspace:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       avatar:
 *                         type: string
 *                   invitedByUser:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       email:
 *                         type: string
 *                       displayName:
 *                         type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
export async function GET(_request: NextRequest) {
  try {
    // Get the authenticated user from Firebase
    const { ok, uid, email } = await isUserAuthenticated();

    if (!ok) {
      return unauthorizedResponse();
    }

    // Get all pending invitations for the current user
    // Match by userId (if they were invited after registering) or by email
    const invitations = await db.workspaceInvite.findMany({
      where: {
        status: InviteStatus.PENDING,
        OR: [{ userId: uid }, { email: email }],
      },
      include: {
        workspace: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
        invitedByUser: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(invitations);
  } catch (error) {
    console.error('Error getting user invitations:', error);
    return NextResponse.json({ error: 'Failed to get invitations' }, { status: 500 });
  }
}
