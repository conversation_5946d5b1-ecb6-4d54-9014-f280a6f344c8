// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

process.env.NEXT_PUBLIC_LOG_LEVEL = 'NONE';

// Mock @/i18n/navigation
jest.mock('@/i18n/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  usePathname: () => '/',
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn().mockReturnValue(null),
    has: jest.fn().mockReturnValue(false),
  }),
}));

// Mock Firebase
jest.mock('firebase/app', () => {
  return {
    initializeApp: jest.fn().mockReturnValue({
      name: '[DEFAULT]',
      options: {},
    }),
    initializeServerApp: jest.fn().mockReturnValue({
      name: '[DEFAULT]',
      options: {},
    }),
    getApps: jest.fn().mockReturnValue([]),
  };
});

jest.mock('firebase/firestore', () => {
  return {
    getFirestore: jest.fn(),
  };
});

jest.mock('firebase/storage', () => {
  return {
    getStorage: jest.fn(),
  };
});

jest.mock('firebase/auth', () => {
  // Mock user
  const mockUser = {
    uid: 'test-uid',
    email: '<EMAIL>',
    displayName: 'Test User',
    photoURL: 'https://example.com/photo.jpg',
    emailVerified: true,
    getIdToken: jest.fn().mockResolvedValue('mock-id-token'),
  };

  return {
    getAuth: jest.fn().mockReturnValue({
      currentUser: mockUser,
      onAuthStateChanged: jest.fn((callback) => {
        callback(mockUser);
        return jest.fn(); // Unsubscribe function
      }),
      onIdTokenChanged: jest.fn((callback) => {
        callback(mockUser);
        return jest.fn(); // Unsubscribe function
      }),
      signInWithEmailAndPassword: jest.fn().mockResolvedValue({ user: mockUser }),
      signInWithPopup: jest.fn().mockResolvedValue({ user: mockUser }),
      createUserWithEmailAndPassword: jest.fn().mockResolvedValue({ user: mockUser }),
      signOut: jest.fn().mockResolvedValue(undefined),
      sendPasswordResetEmail: jest.fn().mockResolvedValue(undefined),
    }),
    GoogleAuthProvider: jest.fn().mockImplementation(() => ({
      setCustomParameters: jest.fn(),
    })),
    GithubAuthProvider: jest.fn().mockImplementation(() => ({
      setCustomParameters: jest.fn(),
    })),
  };
});

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: () => (key) => key,
  getTranslations: () => Promise.resolve((key) => key),
  useLocale: () => 'en',
  getLocale: () => Promise.resolve('en'),
  useFormatter: () => ({
    dateTime: (value) => value,
    number: (value) => value,
    relativeTime: (value) => value,
    list: (value) => value,
    dateTimeRange: (value) => value,
  }),
  NextIntlClientProvider: jest.fn().mockImplementation(({ children }) => children),
  getTimeZone: () => Promise.resolve('UTC'),
}));

// Mock next-intl/server
jest.mock('next-intl/server', () => ({
  useTranslations: () => (key) => key,
  getTranslations: () => Promise.resolve((key) => key),
  getLocale: () => Promise.resolve('en'),
  getMessages: () => Promise.resonext / navigationlve({}),
  getNow: () => Promise.resolve(new Date()),
  useFormatter: () => ({
    dateTime: (value) => value,
    number: (value) => value,
    relativeTime: (value) => value,
    list: (value) => value,
    dateTimeRange: (value) => value,
  }),
  getTimeZone: () => Promise.resolve('UTC'),
}));

// Mock cookies-next
jest.mock('cookies-next', () => ({
  setCookie: jest.fn(),
  getCookie: jest.fn(),
  deleteCookie: jest.fn(),
}));

// Mock next/headers
jest.mock('next/headers', () => ({
  cookies: jest.fn().mockReturnValue({
    get: jest.fn().mockImplementation((name) => {
      if (name === '__session') {
        return { value: 'mock-session-token' };
      }
      return undefined;
    }),
  }),
}));

// Mock logger
jest.mock('@/lib/logger/default-logger');

// Suppress console errors during tests
global.console.error = jest.fn();

// Reset all mocks after each test
afterEach(() => {
  jest.clearAllMocks();
});
