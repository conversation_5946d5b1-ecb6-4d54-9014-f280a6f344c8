import { MainNav } from '@/components/layout/main-nav';
import { Container } from '@mui/system';
import * as React from 'react';

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps): React.JSX.Element {
  return (
    <>
      <MainNav enableMobileNav={false} showSearch={false} showLogo={true} />
      <Container maxWidth={false} sx={{ py: '24px' }}>
        {children}
      </Container>
    </>
  );
}
