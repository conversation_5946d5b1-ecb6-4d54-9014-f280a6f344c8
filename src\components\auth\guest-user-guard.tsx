'use client';

import { useRouter } from '@/i18n/navigation';
import * as React from 'react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { logger } from '@/lib/logger/default-logger';
import { paths } from '@/paths';
import { useSearchParams } from 'next/navigation';
import { FullLoader } from '../layout/full-loader';

export interface GuestUserGuardProps {
  children: React.ReactNode;
}

export function GuestUserGuard({ children }: GuestUserGuardProps): React.JSX.Element | null {
  const router = useRouter();
  const params = useSearchParams();
  const { user, error, loading } = useAuth();
  const [isChecking, setIsChecking] = React.useState<boolean>(true);

  const checkPermissions = async (): Promise<void> => {
    if (loading) {
      return;
    }

    if (error) {
      setIsChecking(false);
      return;
    }

    if (user) {
      const returnTo = params.get('returnTo') ?? '';
      const currentPath = window.location.pathname;

      // Allow authenticated users to access the landing page
      if (currentPath === '/' || currentPath === paths.landing) {
        logger.debug(`[GuestUserGuard]: User is logged in but accessing landing page, allowing access`);
        setIsChecking(false);
        return;
      }

      logger.debug(`[GuestUserGuard]: User is logged in, redirecting to ${returnTo ? 'returnTo' : 'start'}`, {
        returnTo,
        currentPath,
      });

      if (returnTo) {
        router.replace(returnTo as any);
        return;
      }

      // Redirect to start after successful login (but only if not on landing page)
      router.push(paths.root);

      return;
    }

    setIsChecking(false);
  };

  React.useEffect(() => {
    checkPermissions().catch(() => {
      // noop
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps -- Expected
  }, [user, error, loading]);

  if (loading || isChecking) {
    return <FullLoader />;
  }

  // GuestUserGuard should only handle authentication state and redirection
  // Form-specific errors should be handled by the form components
  return <React.Fragment>{children}</React.Fragment>;
}
