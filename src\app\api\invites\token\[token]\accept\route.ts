import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { isUserAuthenticated } from '@/lib/auth/authentication';
import { unauthorizedResponse } from '@/lib/auth/responses';
import { hashToken, isValidToken } from '@/lib/utils/token';
import { db } from '@/services/db';
import { InviteStatus } from '@prisma/client';

/**
 * @swagger
 * /api/invites/token/{token}/accept:
 *   post:
 *     summary: Accept invitation by token
 *     description: Accept a workspace invitation using the token from email links. User must be authenticated.
 *     tags:
 *       - Invitations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation token
 *     responses:
 *       200:
 *         description: Invitation accepted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 workspace:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                 membership:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     role:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *       400:
 *         description: Invalid token format or invitation cannot be accepted
 *       401:
 *         description: Unauthorized - user must be authenticated
 *       403:
 *         description: Forbidden - email mismatch or insufficient permissions
 *       404:
 *         description: Invitation not found or expired
 *       410:
 *         description: Invitation already processed
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
): Promise<NextResponse> {
  try {
    const { token } = await params;

    // Validate token format
    if (!isValidToken(token)) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 400 });
    }

    const { ok, uid, email } = await isUserAuthenticated();
    if (!ok) {
      return unauthorizedResponse();
    }

    // Find the invitation by token (hash the token before comparing)
    const invitation = await db.workspaceInvite.findFirst({
      where: {
        token: hashToken(token),
      },
      include: {
        workspace: {
          select: {
            id: true,
            name: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found or expired' }, { status: 404 });
    }

    // Check if invitation is still pending
    if (invitation.status !== InviteStatus.PENDING) {
      return NextResponse.json(
        {
          error: 'Invitation already processed',
          status: invitation.status,
        },
        { status: 410 }
      );
    }

    // Verify that the authenticated user's email matches the invitation email
    if (email !== invitation.email) {
      return NextResponse.json(
        { error: 'Email mismatch - this invitation is for a different email address' },
        { status: 403 }
      );
    }

    // Ensure workspaceId and roleId are not null
    if (!invitation.workspaceId) {
      return NextResponse.json({ error: 'Invalid invitation - missing workspace' }, { status: 400 });
    }

    if (!invitation.roleId) {
      return NextResponse.json({ error: 'Invalid invitation - missing role' }, { status: 400 });
    }

    // Check if user is already a member of the workspace
    const existingMembership = await db.workspaceMembership.findFirst({
      where: {
        workspaceId: invitation.workspaceId,
        userId: uid,
      },
    });

    if (existingMembership) {
      // Update invitation status to accepted even if already a member
      await db.workspaceInvite.update({
        where: { id: invitation.id },
        data: { status: InviteStatus.ACCEPTED },
      });

      return NextResponse.json({ error: 'You are already a member of this workspace' }, { status: 400 });
    }

    // Use a transaction to ensure data consistency
    const result = await db.$transaction(async (tx) => {
      // Create workspace membership
      const membership = await tx.workspaceMembership.create({
        data: {
          userId: uid,
          workspaceId: invitation.workspaceId!,
          roleId: invitation.roleId!,
        },
        include: {
          role: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Update invitation status
      await tx.workspaceInvite.update({
        where: { id: invitation.id },
        data: {
          status: InviteStatus.ACCEPTED,
          userId: uid,
        },
      });

      return membership;
    });

    return NextResponse.json(
      {
        message: 'Invitation accepted successfully',
        workspace: invitation.workspace,
        membership: result,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error accepting invitation by token:', error);
    return NextResponse.json({ error: 'Failed to accept invitation' }, { status: 500 });
  }
}
