import { Permission } from '@prisma/client';
import { NextResponse } from 'next/server';

export async function forbiddenResponse(missingPermissions?: Permission[]) {
  if (missingPermissions) {
    return NextResponse.json({ error: 'Forbidden', missingPermissions }, { status: 403 });
  }

  return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
}

export async function unauthorizedResponse() {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
}
