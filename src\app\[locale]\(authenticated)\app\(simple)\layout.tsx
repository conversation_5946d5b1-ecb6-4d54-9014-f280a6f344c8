import Container from '@mui/material/Container';
import * as React from 'react';

import { MainNav } from '@/components/layout/main-nav';

interface LayoutProps {
  children: React.ReactNode;
}

/**
 * Simple layout component for pages that need a basic header with logo and logout button
 * Used by onboarding and workspace selection pages
 */
export default function Layout({ children }: LayoutProps): React.JSX.Element {
  return (
    <>
      <MainNav showSearch={false} showLogo={true} enableMobileNav={false} />
      <main>
        <Container maxWidth={false} sx={{ py: '24px' }}>
          {children}
        </Container>
      </main>
    </>
  );
}
