/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

// Mock dependencies
jest.mock('@/services/db', () => ({
  db: {
    workspaceMembership: {
      findFirst: jest.fn(),
    },
    workspaceInvite: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    user: {
      findFirst: jest.fn(),
    },
    role: {
      findFirst: jest.fn(),
    },
  },
}));

jest.mock('@/lib/auth/authentication', () => ({
  isUserAuthenticated: jest.fn(),
}));

jest.mock('@/lib/auth/authorization', () => ({
  checkUserPermission: jest.fn(),
}));

// Import mocked modules
import { isUserAuthenticated } from '@/lib/auth/authentication';
import { checkUserPermission } from '@/lib/auth/authorization';
import { db } from '@/services/db';

// Import the route handlers
import { GET, POST } from '../route';

const mockDb = db as jest.Mocked<typeof db>;
const mockIsUserAuthenticated = isUserAuthenticated as jest.MockedFunction<typeof isUserAuthenticated>;
const mockCheckUserPermission = checkUserPermission as jest.MockedFunction<typeof checkUserPermission>;

// Type cast the specific database methods to jest mocks
const mockWorkspaceInviteFindMany = mockDb.workspaceInvite.findMany as jest.MockedFunction<
  typeof mockDb.workspaceInvite.findMany
>;
const mockWorkspaceInviteFindFirst = mockDb.workspaceInvite.findFirst as jest.MockedFunction<
  typeof mockDb.workspaceInvite.findFirst
>;
const mockWorkspaceInviteCreate = mockDb.workspaceInvite.create as jest.MockedFunction<
  typeof mockDb.workspaceInvite.create
>;
const mockUserFindFirst = mockDb.user.findFirst as jest.MockedFunction<typeof mockDb.user.findFirst>;
const mockRoleFindFirst = mockDb.role.findFirst as jest.MockedFunction<typeof mockDb.role.findFirst>;
const mockWorkspaceMembershipFindFirst = mockDb.workspaceMembership.findFirst as jest.MockedFunction<
  typeof mockDb.workspaceMembership.findFirst
>;

// Helper function to create mock request
function createMockRequest(method: string, body: any, token?: string): NextRequest {
  const url = 'http://localhost:3000/api/workspace/workspace-1/invites';
  const headers = new Headers();
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  headers.set('Content-Type', 'application/json');

  return new NextRequest(url, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });
}

// Mock data
const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'user-1',
  userId: null,
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
  invitedByUser: {
    id: 'user-1',
    email: '<EMAIL>',
    displayName: 'Test User',
  },
  user: null,
  role: { id: 'member', name: 'Member' },
};

const mockRole = {
  id: 'member',
  name: 'Member',
  workspaceId: 'workspace-1',
};

describe('/api/workspace/[workspaceId]/invites', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default to authenticated user with permissions
    mockIsUserAuthenticated.mockResolvedValue({
      ok: true,
      uid: 'user-1',
      email: '<EMAIL>',
    } as any);
    mockCheckUserPermission.mockResolvedValue(true);
  });

  describe('GET', () => {
    it('should return workspace invitations for authorized user', async () => {
      mockWorkspaceInviteFindMany.mockResolvedValue([mockInvitation] as any);

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual([
        {
          ...mockInvitation,
          createdAt: mockInvitation.createdAt.toISOString(),
          updatedAt: mockInvitation.updatedAt.toISOString(),
        },
      ]);
      expect(mockWorkspaceInviteFindMany).toHaveBeenCalledWith({
        where: { workspaceId: 'workspace-1' },
        include: {
          invitedByUser: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          user: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          role: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should return 401 for unauthenticated user', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: false, uid: '', email: '' } as any);

      const request = createMockRequest('GET', null);
      const response = await GET(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(401);
    });

    it('should return 403 for user without permission', async () => {
      mockCheckUserPermission.mockResolvedValue(false);

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(403);
    });
  });

  describe('POST', () => {
    const validInviteData = {
      email: '<EMAIL>',
      roleId: 'member',
    };

    it('should create invitation for new user', async () => {
      // Mock the database calls
      mockUserFindFirst.mockResolvedValue(null); // New user
      mockRoleFindFirst.mockResolvedValue(mockRole as any);
      mockWorkspaceMembershipFindFirst.mockResolvedValue(null); // No existing membership
      mockWorkspaceInviteFindFirst.mockResolvedValue(null); // No existing invitation
      mockWorkspaceInviteCreate.mockResolvedValue(mockInvitation as any);

      const request = createMockRequest('POST', validInviteData, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        ...mockInvitation,
        createdAt: mockInvitation.createdAt.toISOString(),
        updatedAt: mockInvitation.updatedAt.toISOString(),
      });
      expect(mockWorkspaceInviteCreate).toHaveBeenCalledWith({
        data: {
          email: '<EMAIL>',
          workspaceId: 'workspace-1',
          invitedBy: 'user-1',
          userId: null,
          roleId: 'member',
          status: 'PENDING',
          token: expect.any(String), // Should be hashed token
        },
        include: {
          invitedByUser: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          user: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          role: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    });

    it('should return 400 for invalid email', async () => {
      const request = createMockRequest('POST', { email: 'invalid-email', roleId: 'member' }, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for missing roleId', async () => {
      const request = createMockRequest('POST', { email: '<EMAIL>' }, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for invalid role', async () => {
      mockRoleFindFirst.mockResolvedValue(null);

      const request = createMockRequest('POST', { email: '<EMAIL>', roleId: 'invalid-role' }, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for existing invitation', async () => {
      mockUserFindFirst.mockResolvedValue(null);
      mockRoleFindFirst.mockResolvedValue(mockRole as any);
      mockWorkspaceMembershipFindFirst.mockResolvedValue(null);
      mockWorkspaceInviteFindFirst.mockResolvedValue(mockInvitation as any); // Existing invitation

      const request = createMockRequest('POST', validInviteData, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 401 for unauthenticated user', async () => {
      mockIsUserAuthenticated.mockResolvedValue({ ok: false } as any);

      const request = createMockRequest('POST', validInviteData);
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(401);
    });

    it('should return 403 for user without permission', async () => {
      mockCheckUserPermission.mockResolvedValue(false);

      const request = createMockRequest('POST', validInviteData, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(403);
    });
  });
});
