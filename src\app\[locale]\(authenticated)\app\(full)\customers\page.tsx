import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { DownloadIcon } from '@phosphor-icons/react/dist/ssr/Download';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import { UploadIcon } from '@phosphor-icons/react/dist/ssr/Upload';
import type { Metadata } from 'next';
import * as React from 'react';

import { CustomersFilters } from '@/components/customer/customers-filters';
import type { Customer } from '@/components/customer/customers-table';
import { CustomersTable } from '@/components/customer/customers-table';
import { config } from '@/config';
import { DateTime } from 'luxon';

export const metadata = {
  title: `Customers | ${config.site.name}`,
} satisfies Metadata;

const customers = [
  {
    id: 'USR-010',
    name: 'Alcides <PERSON>',
    avatar: '/assets/avatar-10.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Madrid',
      country: 'Spain',
      state: 'Comunidad de Madrid',
      street: '4158 Hedge Street',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-009',
    name: 'Marcus Finn',
    avatar: '/assets/avatar-9.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Carson City',
      country: 'USA',
      state: 'Nevada',
      street: '2188 Armbrester Drive',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-008',
    name: 'Jie Yan',
    avatar: '/assets/avatar-8.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'North Canton',
      country: 'USA',
      state: 'Ohio',
      street: '4894 Lakeland Park Drive',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-007',
    name: 'Nasimiyu Danai',
    avatar: '/assets/avatar-7.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Salt Lake City',
      country: 'USA',
      state: 'Utah',
      street: '368 Lamberts Branch Road',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-006',
    name: 'Iulia Albu',
    avatar: '/assets/avatar-6.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Murray',
      country: 'USA',
      state: 'Utah',
      street: '3934 Wildrose Lane',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-005',
    name: 'Fran Perez',
    avatar: '/assets/avatar-5.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Atlanta',
      country: 'USA',
      state: 'Georgia',
      street: '1865 Pleasant Hill Road',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },

  {
    id: 'USR-004',
    name: 'Penjani Inyene',
    avatar: '/assets/avatar-4.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Berkeley',
      country: 'USA',
      state: 'California',
      street: '317 Angus Road',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-003',
    name: 'Carson Darrin',
    avatar: '/assets/avatar-3.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Cleveland',
      country: 'USA',
      state: 'Ohio',
      street: '2849 Fulton Street',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-002',
    name: 'Siegbert Gottfried',
    avatar: '/assets/avatar-2.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'Los Angeles',
      country: 'USA',
      state: 'California',
      street: '1798 Hickory Ridge Drive',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
  {
    id: 'USR-001',
    name: 'Miron Vitold',
    avatar: '/assets/avatar-1.png',
    email: '<EMAIL>',
    phone: '************',
    address: {
      city: 'San Diego',
      country: 'USA',
      state: 'California',
      street: '75247',
    },
    createdAt: DateTime.now().minus({ hours: 2 }).toJSDate(),
  },
] satisfies Customer[];

export default function Page(): React.JSX.Element {
  const page = 0;
  const rowsPerPage = 5;

  const paginatedCustomers = applyPagination(customers, page, rowsPerPage);

  return (
    <Stack spacing={3}>
      <Stack direction='row' spacing={3}>
        <Stack spacing={1} sx={{ flex: '1 1 auto' }}>
          <Typography variant='h4'>Customers</Typography>
          <Stack direction='row' spacing={1} sx={{ alignItems: 'center' }}>
            <Button color='inherit' startIcon={<UploadIcon fontSize='var(--icon-fontSize-md)' />}>
              Import
            </Button>
            <Button color='inherit' startIcon={<DownloadIcon fontSize='var(--icon-fontSize-md)' />}>
              Export
            </Button>
          </Stack>
        </Stack>
        <div>
          <Button startIcon={<PlusIcon fontSize='var(--icon-fontSize-md)' />} variant='contained'>
            Add
          </Button>
        </div>
      </Stack>
      <CustomersFilters />
      <CustomersTable
        count={paginatedCustomers.length}
        page={page}
        rows={paginatedCustomers}
        rowsPerPage={rowsPerPage}
      />
    </Stack>
  );
}

function applyPagination(rows: Customer[], page: number, rowsPerPage: number): Customer[] {
  return rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
}
